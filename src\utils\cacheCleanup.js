/**
 * 缓存清理工具
 * 用于清理各种不必要的缓存数据
 */

/**
 * 清理 Vue DevTools 相关的缓存
 */
export function clearVueDevToolsCache() {
  try {
    // 清理 Vue DevTools Pinia 插件设置
    const devToolsKeys = Object.keys(localStorage).filter(key => 
      key.includes('__VUE_DEVTOOLS_') || 
      key.includes('dev.esm.pinia') ||
      key.includes('devtools')
    );
    
    devToolsKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`已清理 Vue DevTools 缓存: ${key}`);
    });
    
    return devToolsKeys.length;
  } catch (error) {
    console.error('清理 Vue DevTools 缓存时出错:', error);
    return 0;
  }
}

/**
 * 清理过期的 sessionStorage 数据
 */
export function clearExpiredSessionStorage() {
  try {
    const expiredKeys = [];
    
    // 清理 prevTemplateCardId（已不再使用）
    if (sessionStorage.getItem('prevTemplateCardId')) {
      sessionStorage.removeItem('prevTemplateCardId');
      expiredKeys.push('prevTemplateCardId');
      console.log('已清理过期的 prevTemplateCardId');
    }
    
    // 可以添加其他过期的 sessionStorage 键
    const otherExpiredKeys = [
      'old_template_cache',
      'deprecated_settings',
      // 添加其他需要清理的键
    ];
    
    otherExpiredKeys.forEach(key => {
      if (sessionStorage.getItem(key)) {
        sessionStorage.removeItem(key);
        expiredKeys.push(key);
        console.log(`已清理过期的 sessionStorage: ${key}`);
      }
    });
    
    return expiredKeys.length;
  } catch (error) {
    console.error('清理过期 sessionStorage 时出错:', error);
    return 0;
  }
}

/**
 * 清理点击事件缓存中的无效数据
 */
export function cleanupClickEventCache() {
  try {
    if (!window.CLICK_EVENT_CACHE) {
      console.log('点击事件缓存不存在，无需清理');
      return 0;
    }
    
    const allKeys = Object.keys(window.CLICK_EVENT_CACHE);
    const invalidKeys = [];
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    allKeys.forEach(key => {
      const cacheItem = window.CLICK_EVENT_CACHE[key];
      
      // 检查是否有时间戳且已过期
      if (cacheItem && cacheItem._lastUpdated) {
        const age = now - cacheItem._lastUpdated;
        if (age > maxAge) {
          invalidKeys.push(key);
          delete window.CLICK_EVENT_CACHE[key];
        }
      }
      
      // 检查是否有无效的缓存键格式
      if (!key.includes('_') || key.split('_').length < 3) {
        invalidKeys.push(key);
        delete window.CLICK_EVENT_CACHE[key];
      }
    });
    
    if (invalidKeys.length > 0) {
      console.log(`已清理 ${invalidKeys.length} 个无效的点击事件缓存项:`, invalidKeys);
    }
    
    return invalidKeys.length;
  } catch (error) {
    console.error('清理点击事件缓存时出错:', error);
    return 0;
  }
}

/**
 * 获取缓存使用情况统计
 */
export function getCacheStats() {
  const stats = {
    localStorage: {
      total: 0,
      devTools: 0,
      other: 0
    },
    sessionStorage: {
      total: 0,
      expired: 0,
      valid: 0
    },
    clickEventCache: {
      total: 0,
      valid: 0,
      expired: 0
    }
  };
  
  try {
    // localStorage 统计
    const localStorageKeys = Object.keys(localStorage);
    stats.localStorage.total = localStorageKeys.length;
    stats.localStorage.devTools = localStorageKeys.filter(key => 
      key.includes('__VUE_DEVTOOLS_') || 
      key.includes('dev.esm.pinia') ||
      key.includes('devtools')
    ).length;
    stats.localStorage.other = stats.localStorage.total - stats.localStorage.devTools;
    
    // sessionStorage 统计
    const sessionStorageKeys = Object.keys(sessionStorage);
    stats.sessionStorage.total = sessionStorageKeys.length;
    stats.sessionStorage.expired = sessionStorageKeys.filter(key => 
      key === 'prevTemplateCardId' || 
      key === 'old_template_cache' ||
      key === 'deprecated_settings'
    ).length;
    stats.sessionStorage.valid = stats.sessionStorage.total - stats.sessionStorage.expired;
    
    // 点击事件缓存统计
    if (window.CLICK_EVENT_CACHE) {
      const cacheKeys = Object.keys(window.CLICK_EVENT_CACHE);
      stats.clickEventCache.total = cacheKeys.length;
      
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24小时
      
      cacheKeys.forEach(key => {
        const cacheItem = window.CLICK_EVENT_CACHE[key];
        if (cacheItem && cacheItem._lastUpdated) {
          const age = now - cacheItem._lastUpdated;
          if (age > maxAge) {
            stats.clickEventCache.expired++;
          } else {
            stats.clickEventCache.valid++;
          }
        } else {
          stats.clickEventCache.valid++;
        }
      });
    }
    
  } catch (error) {
    console.error('获取缓存统计时出错:', error);
  }
  
  return stats;
}

/**
 * 执行完整的缓存清理
 */
export function performFullCacheCleanup() {
  console.log('开始执行完整的缓存清理...');
  
  const results = {
    devToolsCleared: 0,
    sessionStorageCleared: 0,
    clickEventCacheCleared: 0,
    totalCleared: 0
  };
  
  // 清理 Vue DevTools 缓存
  results.devToolsCleared = clearVueDevToolsCache();
  
  // 清理过期的 sessionStorage
  results.sessionStorageCleared = clearExpiredSessionStorage();
  
  // 清理点击事件缓存
  results.clickEventCacheCleared = cleanupClickEventCache();
  
  results.totalCleared = results.devToolsCleared + results.sessionStorageCleared + results.clickEventCacheCleared;
  
  console.log('缓存清理完成:', results);
  
  return results;
}

/**
 * 在开发环境中显示缓存清理界面
 */
export function showCacheCleanupUI() {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('缓存清理界面仅在开发环境中可用');
    return;
  }
  
  const stats = getCacheStats();
  
  console.group('🧹 缓存清理工具');
  console.log('📊 当前缓存统计:');
  console.table(stats);
  
  console.log('🔧 可用的清理命令:');
  console.log('clearVueDevToolsCache() - 清理 Vue DevTools 缓存');
  console.log('clearExpiredSessionStorage() - 清理过期的 sessionStorage');
  console.log('cleanupClickEventCache() - 清理点击事件缓存');
  console.log('performFullCacheCleanup() - 执行完整清理');
  
  console.groupEnd();
  
  // 将函数暴露到全局作用域以便在控制台中使用
  window.cacheCleanup = {
    clearVueDevToolsCache,
    clearExpiredSessionStorage,
    cleanupClickEventCache,
    performFullCacheCleanup,
    getCacheStats
  };
  
  console.log('💡 提示: 使用 window.cacheCleanup.performFullCacheCleanup() 执行完整清理');
}

// 在开发环境中自动显示清理界面
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // 延迟显示，确保页面加载完成
  setTimeout(() => {
    showCacheCleanupUI();
  }, 2000);
}