/**
 * 统一点击事件管理系统
 * 集中管理所有点击事件类型、映射关系、验证和生成逻辑
 */

// 点击事件类型定义
export const CLICK_EVENT_TYPES = {
  OPEN_BROWSER: 'OPEN_BROWSER',
  OPEN_APP: 'OPEN_APP',
  OPEN_URL: 'OPEN_URL',
  OPEN_QUICK: 'OPEN_QUICK',
  DIAL_PHONE: 'DIAL_PHONE',
  COPY_PARAMETER: 'COPY_PARAMETER',
  OPEN_SMS: 'OPEN_SMS',
  OPEN_EMAIL: 'OPEN_EMAIL',
  OPEN_SCHEDULE: 'OPEN_SCHEDULE',
  OPEN_POPUP: 'OPEN_POPUP'
};

// 事件类型配置信息
export const CLICK_EVENT_CONFIG = {
  [CLICK_EVENT_TYPES.OPEN_BROWSER]: {
    label: '打开浏览器',
    tooltip: '点击时打开浏览器访问指定链接',
    placeholder: 'https://开头，字符长度<=3000',
    requiresUrl: true,
    requiresPath: false,
    additionalFields: []
  },
  [CLICK_EVENT_TYPES.OPEN_APP]: {
    label: '打开APP',
    tooltip: '点击时打开指定APP。注意部分APP需要先安装才能打开',
    placeholder: '[scheme]://开头，字符长度<=3000',
    requiresUrl: true,
    requiresPath: false,
    additionalFields: ['packageName', 'floorType']
  },
  [CLICK_EVENT_TYPES.OPEN_URL]: {
    label: '打开链接',
    tooltip: '点击时打开指定的链接或URL',
    placeholder: 'https://开头，字符长度<=3000',
    requiresUrl: true,
    requiresPath: false,
    additionalFields: []
  },
  [CLICK_EVENT_TYPES.OPEN_QUICK]: {
    label: '打开快应用',
    tooltip: '点击时打开华为等厂商的快应用',
    placeholder: 'hap://app/开头，字符长度<=3000',
    requiresUrl: true,
    requiresPath: false,
    additionalFields: []
  },
  [CLICK_EVENT_TYPES.DIAL_PHONE]: {
    label: '拨打电话',
    tooltip: '点击时调起电话功能拨打指定号码',
    placeholder: '请输入电话号码',
    requiresUrl: true,
    requiresPath: false,
    additionalFields: []
  },
  [CLICK_EVENT_TYPES.COPY_PARAMETER]: {
    label: '复制内容',
    tooltip: '点击时复制参数或指定内容到剪贴板',
    placeholder: '',
    requiresUrl: false,
    requiresPath: false,
    additionalFields: ['copyType', 'selectedParamId', 'fixedContent']
  },
  [CLICK_EVENT_TYPES.OPEN_SMS]: {
    label: '发送短信',
    tooltip: '点击时调起短信编辑窗口发送到指定号码',
    placeholder: '请输入电话号码',
    requiresUrl: true,
    requiresPath: true,
    additionalFields: []
  },
  [CLICK_EVENT_TYPES.OPEN_EMAIL]: {
    label: '跳转邮箱',
    tooltip: '点击时调起邮件编辑窗口发送到指定邮箱',
    placeholder: '',
    requiresUrl: false,
    requiresPath: false,
    additionalFields: ['emailAddress', 'emailSubject', 'emailBody']
  },
  [CLICK_EVENT_TYPES.OPEN_SCHEDULE]: {
    label: '跳转日程',
    tooltip: '点击时调起日历添加日程功能',
    placeholder: '',
    requiresUrl: false,
    requiresPath: false,
    additionalFields: ['scheduleTitle', 'scheduleContent', 'scheduleStartTimeString', 'scheduleEndTimeString']
  },
  [CLICK_EVENT_TYPES.OPEN_POPUP]: {
    label: '打开弹窗',
    tooltip: '该事件为响应后在手机终端弹出弹窗，弹窗的内容为配置内容',
    placeholder: '',
    requiresUrl: false,
    requiresPath: false,
    additionalFields: ['popupTitle', 'popupContent', 'popupButtonText']
  }
};

/**
 * 事件类型映射关系
 * 
 * 映射规则：
 * 1. 每个 actionType 对应一个唯一的 clickEvent.type
 * 2. 每个 clickEvent.type 只映射到一个 actionType
 * 3. 统一使用新的映射关系，不兼容旧版本
 * 
 * 标准映射关系：
 * - OPEN_BROWSER ↔ 'browser'     (打开浏览器)
 * - OPEN_URL ↔ 'url'            (打开链接)
 * - OPEN_APP ↔ 'app'            (打开APP)
 * - DIAL_PHONE ↔ 'phone'        (拨打电话)
 * - OPEN_SMS ↔ 'sms'            (发送短信)
 * - OPEN_EMAIL ↔ 'email'        (跳转邮箱)
 * - OPEN_SCHEDULE ↔ 'schedule'  (跳转日程)
 * - OPEN_POPUP ↔ 'popup'        (打开弹窗)
 * - OPEN_QUICK ↔ 'quick'        (打开快应用)
 * - COPY_PARAMETER ↔ 'copy'     (复制内容)
 */
// 事件类型映射关系
export const CLICK_EVENT_MAPPINGS = {
  // actionType 到 clickEvent.type 的映射
  actionTypeToClickEventType: {
    [CLICK_EVENT_TYPES.OPEN_BROWSER]: 'browser',
    [CLICK_EVENT_TYPES.OPEN_URL]: 'url',
    [CLICK_EVENT_TYPES.OPEN_APP]: 'app',
    [CLICK_EVENT_TYPES.DIAL_PHONE]: 'phone',
    [CLICK_EVENT_TYPES.OPEN_SMS]: 'sms',
    [CLICK_EVENT_TYPES.OPEN_EMAIL]: 'email',
    [CLICK_EVENT_TYPES.OPEN_SCHEDULE]: 'schedule',
    [CLICK_EVENT_TYPES.OPEN_POPUP]: 'popup',
    [CLICK_EVENT_TYPES.OPEN_QUICK]: 'quick',
    [CLICK_EVENT_TYPES.COPY_PARAMETER]: 'copy'
  },
  
  // clickEvent.type 到 actionType 的映射 - 一对一映射
  clickEventTypeToActionType: {
    'browser': CLICK_EVENT_TYPES.OPEN_BROWSER,
    'url': CLICK_EVENT_TYPES.OPEN_URL,
    'app': CLICK_EVENT_TYPES.OPEN_APP,
    'phone': CLICK_EVENT_TYPES.DIAL_PHONE,
    'sms': CLICK_EVENT_TYPES.OPEN_SMS,
    'email': CLICK_EVENT_TYPES.OPEN_EMAIL,
    'schedule': CLICK_EVENT_TYPES.OPEN_SCHEDULE,
    'popup': CLICK_EVENT_TYPES.OPEN_POPUP,
    'quick': CLICK_EVENT_TYPES.OPEN_QUICK,
    'copy': CLICK_EVENT_TYPES.COPY_PARAMETER
  }
};

// 默认事件类型
export const DEFAULT_CLICK_EVENT_TYPE = CLICK_EVENT_TYPES.OPEN_BROWSER;

/**
 * 统一的事件类型转换工具
 */
export class ClickEventTypeConverter {
  /**
   * 将任意格式的事件类型转换为标准actionType
   * @param {string} type - 任意格式的事件类型
   * @returns {string} 标准的actionType
   */
  static toActionType(type) {
    if (!type) return DEFAULT_CLICK_EVENT_TYPE;
    
    // 如果已经是标准的actionType，直接返回
    if (CLICK_EVENT_CONFIG[type]) {
      return type;
    }
    
    // 尝试从clickEvent.type映射转换
    const mappedType = CLICK_EVENT_MAPPINGS.clickEventTypeToActionType[type.toLowerCase()];
    if (mappedType) {
      return mappedType;
    }
    
    // 尝试从标签名称转换
    for (const [actionType, config] of Object.entries(CLICK_EVENT_CONFIG)) {
      if (config.label === type) {
        return actionType;
      }
    }
    
    console.warn(`无法识别的点击事件类型: ${type}，使用默认类型`);
    return DEFAULT_CLICK_EVENT_TYPE;
  }
  
  /**
   * 将actionType转换为clickEvent.type
   * @param {string} actionType - 标准的actionType
   * @returns {string} clickEvent.type
   */
  static toClickEventType(actionType) {
    if (!actionType) return 'none';
    
    return CLICK_EVENT_MAPPINGS.actionTypeToClickEventType[actionType] || 'none';
  }
  
  /**
   * 获取事件类型的显示标签
   * @param {string} actionType - 标准的actionType
   * @returns {string} 显示标签
   */
  static getLabel(actionType) {
    return CLICK_EVENT_CONFIG[actionType]?.label || actionType;
  }
  
  /**
   * 获取事件类型的提示信息
   * @param {string} actionType - 标准的actionType
   * @returns {string} 提示信息
   */
  static getTooltip(actionType) {
    return CLICK_EVENT_CONFIG[actionType]?.tooltip || '';
  }
  
  /**
   * 获取事件类型的占位符
   * @param {string} actionType - 标准的actionType
   * @returns {string} 占位符
   */
  static getPlaceholder(actionType) {
    return CLICK_EVENT_CONFIG[actionType]?.placeholder || '';
  }
}

/**
 * 统一的actionJson生成器
 */
export class ActionJsonGenerator {
  /**
   * 根据事件类型和参数生成actionJson
   * @param {string} actionType - 事件类型
   * @param {string} url - 主要URL或目标
   * @param {string} path - 次要路径或内容
   * @param {Object} additionalData - 额外数据
   * @returns {Object} actionJson对象
   */
  static generate(actionType, url = '', path = '', additionalData = {}) {
    const standardType = ClickEventTypeConverter.toActionType(actionType);
    
    switch (standardType) {
      case CLICK_EVENT_TYPES.OPEN_APP:
        return {
          target: url || '',
          packageName: this._ensureArray(additionalData.packageName || additionalData.package || ''),
          floorType: additionalData.floorType || additionalData.marketType || '0',
          // floorUrl: url || ''
        };
        
      case CLICK_EVENT_TYPES.OPEN_SCHEDULE:
        return {
          target: additionalData.scheduleTitle || additionalData.title || '',
          description: additionalData.scheduleContent || path || additionalData.description || '',
          beginTime: additionalData.scheduleStartTimeString || additionalData.startTime || '',
          endTime: additionalData.scheduleEndTimeString || additionalData.endTime || ''
        };
        
      case CLICK_EVENT_TYPES.OPEN_POPUP:
        return {
          target: additionalData.popupTitle || additionalData.title || url || '',
          content: additionalData.popupContent || additionalData.content || path || '',
          textButton: additionalData.popupButtonText || additionalData.buttonText || '',
          mode: 0
        };
        
      case CLICK_EVENT_TYPES.OPEN_SMS:
        return {
          target: url || '',
          body: path || additionalData.body || ''
        };
        
      case CLICK_EVENT_TYPES.OPEN_EMAIL:
        return {
          target: additionalData.emailAddress || additionalData.address || url || '',
          subject: additionalData.emailSubject || additionalData.subject || '',
          body: additionalData.emailBody || additionalData.body || path || ''
        };
        
      case CLICK_EVENT_TYPES.DIAL_PHONE:
        return {
          target: url || ''
        };
        
      case CLICK_EVENT_TYPES.COPY_PARAMETER:
        return {
          target: additionalData.copyType === '1' ?
            (additionalData.selectedParamId || '') :
            (additionalData.fixedContent || url || '')
        };
        
      case CLICK_EVENT_TYPES.OPEN_BROWSER:
      case CLICK_EVENT_TYPES.OPEN_URL:
      case CLICK_EVENT_TYPES.OPEN_QUICK:
      default:
        return {
          target: url || ''
        };
    }
  }
  
  /**
   * 从clickEvent对象生成actionJson
   * @param {Object} clickEvent - clickEvent对象
   * @returns {Object} actionJson对象
   */
  static fromClickEvent(clickEvent) {
    if (!clickEvent) {
      return { target: '' };
    }
    
    // 处理电商模板的情况：优先使用新格式字段
    let actionType;
    let clickEventType;
    
    // 优先使用新格式字段（电商模板使用）
    if (clickEvent.actionType) {
      actionType = clickEvent.actionType;
      clickEventType = ClickEventTypeConverter.toClickEventType(actionType);
    } else if (clickEvent.type && clickEvent.type !== 'none') {
      // 回退到老格式字段
      if (CLICK_EVENT_CONFIG[clickEvent.type]) {
        // clickEvent.type 是标准的 actionType
        actionType = clickEvent.type;
        clickEventType = ClickEventTypeConverter.toClickEventType(actionType);
      } else {
        // clickEvent.type 是标准的 clickEvent.type
        clickEventType = clickEvent.type;
        actionType = ClickEventTypeConverter.toActionType(clickEventType);
      }
    } else {
      return { target: '' };
    }
    
    let url = '';
    let path = '';
    const additionalData = {};
    
    // 优先使用新格式字段，回退到老格式字段
    switch (clickEventType) {
      case 'browser':
        url = clickEvent.actionUrl || clickEvent.url || clickEvent.browser || clickEvent.target || '';
        break;
      case 'url':
        url = clickEvent.actionUrl || clickEvent.url || clickEvent.target || '';
        break;
      case 'phone':
        url = clickEvent.actionUrl || clickEvent.phone || clickEvent.target || '';
        break;
      case 'copy':
        url = clickEvent.actionUrl || clickEvent.text || clickEvent.target || '';
        // 添加复制相关的额外数据
        additionalData.copyType = clickEvent.copyType || '2'; // 默认为固定内容
        additionalData.selectedParamId = clickEvent.selectedParamId || '';
        additionalData.fixedContent = clickEvent.fixedContent || clickEvent.text || url || '';
        break;
      case 'app':
        url = clickEvent.actionUrl || clickEvent.url || clickEvent.app || clickEvent.target || '';
        additionalData.packageName = clickEvent.packageName || '';
        additionalData.floorType = clickEvent.floorType || '0';
        break;
      case 'quick':
        url = clickEvent.actionUrl || clickEvent.quick || clickEvent.target || '';
        break;
      case 'sms':
        url = clickEvent.actionUrl || clickEvent.phone || clickEvent.target || '';
        path = clickEvent.actionPath || clickEvent.text || clickEvent.smsBody || '';
        break;
      case 'email':
        url = clickEvent.actionUrl || clickEvent.emailAddress || clickEvent.email || clickEvent.target || '';
        path = clickEvent.actionPath || clickEvent.emailSubject || '';
        additionalData.emailAddress = clickEvent.emailAddress || '';
        additionalData.emailSubject = clickEvent.emailSubject || '';
        additionalData.emailBody = clickEvent.emailBody || '';
        break;
      case 'schedule':
        url = clickEvent.actionUrl || clickEvent.scheduleTitle || clickEvent.schedule || clickEvent.target || '';
        path = clickEvent.actionPath || clickEvent.scheduleContent || clickEvent.description || '';
        additionalData.scheduleTitle = clickEvent.scheduleTitle || '';
        additionalData.scheduleContent = clickEvent.scheduleContent || '';
        additionalData.scheduleStartTimeString = clickEvent.scheduleStartTimeString || '';
        additionalData.scheduleEndTimeString = clickEvent.scheduleEndTimeString || '';
        break;
      case 'popup':
        url = clickEvent.actionUrl || clickEvent.popupTitle || clickEvent.popup || clickEvent.target || '';
        path = clickEvent.actionPath || clickEvent.popupContent || '';
        additionalData.popupTitle = clickEvent.popupTitle || '';
        additionalData.popupContent = clickEvent.popupContent || '';
        additionalData.popupButtonText = clickEvent.popupButtonText || '';
        break;
    }
    
    return this.generate(actionType, url, path, additionalData);
  }
  
  /**
   * 从ClickEventSettings组件的格式转换回clickEvent格式
   * @param {Object} settingsContent - ClickEventSettings组件的content对象
   * @returns {Object} clickEvent对象
   */
  static fromClickEventSettings(settingsContent) {
    if (!settingsContent || !settingsContent.actionType) {
      return { type: 'OPEN_BROWSER' };
    }
    
    const actionType = ClickEventTypeConverter.toActionType(settingsContent.actionType);
    const clickEventType = ClickEventTypeConverter.toClickEventType(actionType);
    
    // 基础clickEvent对象
    const clickEvent = {
      type: clickEventType
    };
    
    // 根据actionType收集相应的字段
    switch (actionType) {
      case CLICK_EVENT_TYPES.OPEN_BROWSER:
      case CLICK_EVENT_TYPES.OPEN_URL:
        clickEvent.url = settingsContent.actionUrl || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_APP:
        clickEvent.app = settingsContent.actionUrl || '';
        // 确保packageName是数组格式
        clickEvent.packageName = this._ensureArray(settingsContent.packageName || '');
        clickEvent.floorType = settingsContent.floorType || '0';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_QUICK:
        clickEvent.quick = settingsContent.actionUrl || '';
        break;
        
      case CLICK_EVENT_TYPES.DIAL_PHONE:
        clickEvent.phone = settingsContent.actionUrl || '';
        break;
        
      case CLICK_EVENT_TYPES.COPY_PARAMETER:
        if (settingsContent.copyType === '1') {
          clickEvent.text = settingsContent.selectedParamId || '';
        } else {
          clickEvent.text = settingsContent.fixedContent || '';
        }
        clickEvent.copyType = settingsContent.copyType || '1';
        clickEvent.selectedParamId = settingsContent.selectedParamId || '';
        clickEvent.fixedContent = settingsContent.fixedContent || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_SMS:
        clickEvent.phone = settingsContent.actionUrl || '';
        clickEvent.smsBody = settingsContent.actionPath || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_EMAIL:
        clickEvent.emailAddress = settingsContent.emailAddress || '';
        clickEvent.emailSubject = settingsContent.emailSubject || '';
        clickEvent.emailBody = settingsContent.emailBody || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_SCHEDULE:
        clickEvent.schedule = settingsContent.actionUrl || '';
        clickEvent.description = settingsContent.actionPath || '';
        clickEvent.scheduleTitle = settingsContent.scheduleTitle || '';
        clickEvent.scheduleContent = settingsContent.scheduleContent || '';
        clickEvent.scheduleStartTimeString = settingsContent.scheduleStartTimeString || '';
        clickEvent.scheduleEndTimeString = settingsContent.scheduleEndTimeString || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_POPUP:
        clickEvent.popup = settingsContent.actionUrl || '';
        clickEvent.popupTitle = settingsContent.popupTitle || '';
        clickEvent.popupContent = settingsContent.popupContent || '';
        clickEvent.popupButtonText = settingsContent.popupButtonText || '';
        break;
        
      default:
        clickEvent.url = settingsContent.actionUrl || '';
        break;
    }
    
    return clickEvent;
  }
  
  /**
   * 从actionType和actionJson转换为clickEvent格式
   * @param {string} actionType - 事件类型
   * @param {Object} actionJson - 事件配置
   * @returns {Object} clickEvent对象
   */
  static toClickEvent(actionType, actionJson) {
    if (!actionType || !actionJson) {
      return { type: 'OPEN_BROWSER' };
    }
    
    const clickEventType = ClickEventTypeConverter.toClickEventType(actionType);
    const clickEvent = {
      type: clickEventType
    };
    
    // 根据actionType收集相应的字段
    switch (actionType) {
      case CLICK_EVENT_TYPES.OPEN_BROWSER:
        clickEvent.url = actionJson.target || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_URL:
        clickEvent.url = actionJson.target || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_APP:
        clickEvent.app = actionJson.target || '';
        clickEvent.packageName = actionJson.packageName || '';
        clickEvent.floorType = actionJson.floorType || '0';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_QUICK:
        clickEvent.quick = actionJson.target || '';
        break;
        
      case CLICK_EVENT_TYPES.DIAL_PHONE:
        clickEvent.phone = actionJson.target || '';
        break;
        
      case CLICK_EVENT_TYPES.COPY_PARAMETER:
        clickEvent.text = actionJson.target || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_SMS:
        clickEvent.phone = actionJson.target || '';
        clickEvent.text = actionJson.body || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_EMAIL:
        clickEvent.email = actionJson.target || '';
        clickEvent.emailSubject = actionJson.subject || '';
        clickEvent.emailBody = actionJson.body || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_SCHEDULE:
        clickEvent.schedule = actionJson.target || '';
        clickEvent.text = actionJson.description || '';
        clickEvent.scheduleTitle = actionJson.target || '';
        clickEvent.scheduleContent = actionJson.description || '';
        clickEvent.scheduleStartTimeString = actionJson.beginTime || '';
        clickEvent.scheduleEndTimeString = actionJson.endTime || '';
        break;
        
      case CLICK_EVENT_TYPES.OPEN_POPUP:
        clickEvent.popup = actionJson.target || '';
        clickEvent.popupContent = actionJson.content || '';
        clickEvent.popupButtonText = actionJson.textButton || '';
        break;
        
      default:
        clickEvent.url = actionJson.target || '';
        break;
    }
    
    return clickEvent;
  }

  /**
   * 确保packageName是数组格式
   * @param {string|Array} packageName - 包名
   * @returns {Array} 数组格式的包名
   */
  static _ensureArray(packageName) {
    if (!packageName) return [];
    if (Array.isArray(packageName)) return packageName;
    return [packageName];
  }

  /**
   * 从actionJson中提取actionUrl和actionPath
   * @param {string} actionType - 事件类型
   * @param {Object} actionJson - actionJson对象
   * @returns {Object} 包含actionUrl和actionPath的对象
   */
  static extractActionFields(actionType, actionJson) {
    if (!actionType || !actionJson) {
      return { actionUrl: '', actionPath: '' };
    }

    const standardType = ClickEventTypeConverter.toActionType(actionType);
    let actionUrl = '';
    let actionPath = '';

    switch (standardType) {
      case CLICK_EVENT_TYPES.OPEN_BROWSER:
      case CLICK_EVENT_TYPES.OPEN_URL:
      case CLICK_EVENT_TYPES.OPEN_QUICK:
        actionUrl = actionJson.target || '';
        break;
      case CLICK_EVENT_TYPES.OPEN_APP:
        actionUrl = actionJson.target || '';
        break;
      case CLICK_EVENT_TYPES.DIAL_PHONE:
        actionUrl = actionJson.target || '';
        break;
      case CLICK_EVENT_TYPES.COPY_PARAMETER:
        actionUrl = actionJson.target || '';
        break;
      case CLICK_EVENT_TYPES.OPEN_SMS:
        actionUrl = actionJson.target || '';
        actionPath = actionJson.body || '';
        break;
      case CLICK_EVENT_TYPES.OPEN_EMAIL:
        actionUrl = actionJson.target || '';
        actionPath = actionJson.subject || '';
        break;
      case CLICK_EVENT_TYPES.OPEN_SCHEDULE:
        actionUrl = actionJson.target || '';
        actionPath = actionJson.description || '';
        break;
      case CLICK_EVENT_TYPES.OPEN_POPUP:
        actionUrl = actionJson.target || '';
        actionPath = actionJson.content || '';
        break;
      default:
        actionUrl = actionJson.target || '';
        break;
    }

    return { actionUrl, actionPath };
  }

  /**
   * 从clickEvent生成ClickEventSettings格式的内容
   * @param {Object} clickEvent - clickEvent对象
   * @param {string} contentId - 内容ID
   * @param {string} type - 内容类型
   * @returns {Object} ClickEventSettings格式的内容对象
   */
  static toClickEventSettings(clickEvent, contentId = '', type = '') {
    if (!clickEvent || !clickEvent.type || clickEvent.type === 'none') {
      return {
        contentId,
        type,
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionUrl: '',
        actionPath: '',
        packageName: '',
        floorType: '0',
        emailAddress: '',
        emailSubject: '',
        emailBody: '',
        scheduleTitle: '',
        scheduleContent: '',
        scheduleStartTimeString: '',
        scheduleEndTimeString: '',
        popupTitle: '',
        popupContent: '',
        popupButtonText: '',
        copyType: '1',
        selectedParamId: '',
        fixedContent: ''
      };
    }

    // 使用clickEvent.type而不是参数type来确定actionType
    const actionJson = this.fromClickEvent(clickEvent);
    const actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
    const { actionUrl, actionPath } = this.extractActionFields(actionType, actionJson);

    // 从actionJson中提取额外字段，优先使用actionJson中的数据
    const result = {
      contentId,
      type, // 这个type是组件类型标识符，不是点击事件类型
      actionType,
      actionUrl,
      actionPath,
      packageName: actionJson.packageName || clickEvent.packageName || '',
      floorType: actionJson.floorType || clickEvent.floorType || '0',
      emailAddress: actionJson.emailAddress || clickEvent.emailAddress || '',
      emailSubject: actionJson.emailSubject || clickEvent.emailSubject || '',
      emailBody: actionJson.emailBody || clickEvent.emailBody || '',
      scheduleTitle: actionJson.scheduleTitle || clickEvent.scheduleTitle || '',
      scheduleContent: actionJson.scheduleContent || clickEvent.scheduleContent || '',
      scheduleStartTimeString: actionJson.scheduleStartTimeString || clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: actionJson.scheduleEndTimeString || clickEvent.scheduleEndTimeString || '',
      popupTitle: actionJson.popupTitle || clickEvent.popupTitle || '',
      popupContent: actionJson.popupContent || clickEvent.popupContent || '',
      popupButtonText: actionJson.popupButtonText || clickEvent.popupButtonText || '',
      // 复制参数相关字段
      copyType: actionJson.copyType || clickEvent.copyType || '1',
      selectedParamId: actionJson.selectedParamId || clickEvent.selectedParamId || '',
      fixedContent: actionJson.fixedContent || clickEvent.fixedContent || ''
    };

    // 特殊处理：对于邮箱类型，如果emailAddress为空，使用actionUrl
    if (actionType === CLICK_EVENT_TYPES.OPEN_EMAIL && !result.emailAddress && result.actionUrl) {
      result.emailAddress = result.actionUrl;
    }

    return result;
  }
}

/**
 * 统一的点击事件验证器
 */
export class ClickEventValidator {
  /**
   * 验证点击事件配置
   * @param {Object} content - 内容对象
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validate(content) {
    if (!content) {
      return { valid: false, message: '请设置点击事件' };
    }
    
    const actionType = ClickEventTypeConverter.toActionType(content.actionType);
    const config = CLICK_EVENT_CONFIG[actionType];
    
    if (!config) {
      return { valid: false, message: '无效的点击事件类型' };
    }
    
    // 检查必需的URL
    if (config.requiresUrl && (!content.actionUrl || content.actionUrl.trim() === '')) {
      return { valid: false, message: `请设置${config.label}点击事件` };
    }
    
    // 检查URL格式
    if (config.requiresUrl && content.actionUrl && content.actionUrl.trim() !== '') {
      const url = content.actionUrl.trim();
      
      // 根据不同的actionType检查URL格式
      if (actionType === CLICK_EVENT_TYPES.OPEN_BROWSER || actionType === CLICK_EVENT_TYPES.OPEN_URL) {
        // 检查是否以https://开头
        if (!url.startsWith('https://')) {
          return { valid: false, message: `${config.label}的链接地址必须以https://开头` };
        }
        
        // 检查URL长度
        if (url.length > 3000) {
          return { valid: false, message: `${config.label}的链接地址长度不能超过3000个字符` };
        }
        
        // 检查URL格式是否有效
        // try {
        //   new URL(url);
        // } catch (error) {
        //   return { valid: false, message: `${config.label}的链接地址格式不正确` };
        // }
      } else if (actionType === CLICK_EVENT_TYPES.OPEN_APP) {
        // 检查是否以scheme://开头或https://开头
        if (!url.includes('://')) {
          return { valid: false, message: `${config.label}的链接地址格式不正确，应为[scheme]://开头或https://开头` };
        }
        
        // 检查URL格式是否有效
        // try {
        //   new URL(url);
        // } catch (error) {
        //   return { valid: false, message: `${config.label}的链接地址格式不正确` };
        // }
      } else if (actionType === CLICK_EVENT_TYPES.OPEN_QUICK) {
        // 检查是否以hap://app/开头
        if (!url.startsWith('hap://app/')) {
          return { valid: false, message: `${config.label}的链接地址必须以hap://app/开头` };
        }
      } else if (actionType === CLICK_EVENT_TYPES.DIAL_PHONE) {
        // 检查电话号码格式
        const phoneRegex = /^[\d\-\+\(\)\s]+$/;
        if (!phoneRegex.test(url)) {
          return { valid: false, message: `${config.label}的电话号码格式不正确` };
        }
      } else if (actionType === CLICK_EVENT_TYPES.OPEN_SMS) {
        // 检查电话号码格式
        const phoneRegex = /^[\d\-\+\(\)\s]+$/;
        if (!phoneRegex.test(url)) {
          return { valid: false, message: `${config.label}的电话号码格式不正确` };
        }
      }
    }
    
    // 检查必需的路径
    if (config.requiresPath && (!content.actionPath || content.actionPath.trim() === '')) {
      return { valid: false, message: `请设置${config.label}的内容` };
    }
    
    // 检查额外字段
    for (const field of config.additionalFields) {
      if (field === 'packageName' && actionType === CLICK_EVENT_TYPES.OPEN_APP) {
        if (!content.packageName || (Array.isArray(content.packageName) && content.packageName.length === 0)) {
          return { valid: false, message: '请设置APP包名' };
        }
      }
      
      if (field === 'emailAddress' && actionType === CLICK_EVENT_TYPES.OPEN_EMAIL) {
        if (!content.emailAddress || content.emailAddress.trim() === '') {
          return { valid: false, message: '请设置邮箱地址' };
        }

        // 检查邮箱格式
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(content.emailAddress.trim())) {
          return { valid: false, message: '请输入正确的邮箱地址格式' };
        }
      }
      
      if (field === 'emailSubject' && actionType === CLICK_EVENT_TYPES.OPEN_EMAIL) {
        if (!content.emailSubject || content.emailSubject.trim() === '') {
          return { valid: false, message: '请设置邮件标题' };
        }
      }

      if (field === 'emailBody' && actionType === CLICK_EVENT_TYPES.OPEN_EMAIL) {
        if (!content.emailBody || content.emailBody.trim() === '') {
          return { valid: false, message: '请设置邮件正文' };
        }
      }
      if (field === 'scheduleTitle' && actionType === CLICK_EVENT_TYPES.OPEN_SCHEDULE) {
        if (!content.scheduleTitle || content.scheduleTitle.trim() === '') {
          return { valid: false, message: '请输入跳转日程标题' };
        }
      }

      if (field === 'scheduleContent' && actionType === CLICK_EVENT_TYPES.OPEN_SCHEDULE) {
        if (!content.scheduleContent || content.scheduleContent.trim() === '') {
          return { valid: false, message: '请输入跳转日程内容' };
        }
      }

      if (field === 'scheduleStartTimeString' && actionType === CLICK_EVENT_TYPES.OPEN_SCHEDULE) {
        if (!content.scheduleStartTimeString || content.scheduleStartTimeString.trim() === '') {
          return { valid: false, message: '请设置开始时间' };
        }
      }

      if (field === 'scheduleEndTimeString' && actionType === CLICK_EVENT_TYPES.OPEN_SCHEDULE) {
        if (!content.scheduleEndTimeString || content.scheduleEndTimeString.trim() === '') {
          return { valid: false, message: '请设置结束时间' };
        }
      }
      if (field === 'popupTitle' && actionType === CLICK_EVENT_TYPES.OPEN_POPUP) {
        if (!content.popupTitle || content.popupTitle.trim() === '') {
          return { valid: false, message: '请设置弹窗标题' };
        }
      }
      
      if (field === 'popupContent' && actionType === CLICK_EVENT_TYPES.OPEN_POPUP) {
        if (!content.popupContent || content.popupContent.trim() === '') {
          return { valid: false, message: '请设置弹窗内容' };
        }
      }

      if (field === 'popupButtonText' && actionType === CLICK_EVENT_TYPES.OPEN_POPUP) {
        if (!content.popupButtonText || content.popupButtonText.trim() === '') {
          return { valid: false, message: '请设置弹窗按钮名称' };
        }
      }


    }
    
    return { valid: true, message: '验证通过' };
  }
  
  /**
   * 验证并完善内容对象
   * @param {Object} content - 内容对象
   * @returns {boolean} 是否验证通过
   */
  static validateAndComplete(content) {
    if (!content) return false;
    
    // 设置默认actionType
    if (!content.actionType) {
      content.actionType = DEFAULT_CLICK_EVENT_TYPE;
    }
    
    // 标准化actionType
    content.actionType = ClickEventTypeConverter.toActionType(content.actionType);
    
    // 生成actionJson
    content.actionJson = ActionJsonGenerator.generate(
      content.actionType,
      content.actionUrl,
      content.actionPath,
      content
    );
    
    return true;
  }
}

/**
 * 统一的点击事件缓存管理器
 */
export class ClickEventCacheManager {
  constructor() {
    this.cache = new Map();
  }
  
  /**
   * 获取缓存键
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @returns {string} 缓存键
   */
  static getCacheKey(contentType, contentId) {
    return `${contentType}_${contentId}`;
  }
  
  /**
   * 设置缓存
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @param {Object} data - 缓存数据
   */
  setCache(contentType, contentId, data) {
    const key = ClickEventCacheManager.getCacheKey(contentType, contentId);
    this.cache.set(key, {
      ...data,
      _lastUpdated: Date.now()
    });
  }
  
  /**
   * 获取缓存
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @returns {Object|null} 缓存数据
   */
  getCache(contentType, contentId) {
    const key = ClickEventCacheManager.getCacheKey(contentType, contentId);
    return this.cache.get(key) || null;
  }
  
  /**
   * 清除缓存
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   */
  clearCache(contentType, contentId) {
    const key = ClickEventCacheManager.getCacheKey(contentType, contentId);
    this.cache.delete(key);
  }
  
  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear();
  }
  
  /**
   * 从内容对象同步到缓存
   * @param {Object} content - 内容对象
   */
  syncFromContent(content) {
    if (!content || !content.type) return;
    
    const cacheData = {
      actionType: ClickEventTypeConverter.toActionType(content.actionType),
      actionUrl: content.actionUrl || '',
      actionPath: content.actionPath || '',
      packageName: content.packageName || '',
      floorType: content.floorType || '0',
      emailAddress: content.emailAddress || '',
      emailSubject: content.emailSubject || '',
      emailBody: content.emailBody || '',
      scheduleTitle: content.scheduleTitle || '',
      scheduleContent: content.scheduleContent || '',
      scheduleStartTimeString: content.scheduleStartTimeString || '',
      scheduleEndTimeString: content.scheduleEndTimeString || '',
      popupTitle: content.popupTitle || '',
      popupContent: content.popupContent || '',
      popupButtonText: content.popupButtonText || '',
      copyType: content.copyType || '1',
      selectedParamId: content.selectedParamId || '',
      fixedContent: content.fixedContent || ''
    };
    
    this.setCache(content.type, content.contentId || '', cacheData);
  }
  
  /**
   * 从缓存同步到内容对象
   * @param {Object} content - 内容对象
   */
  syncToContent(content) {
    if (!content || !content.type) return;
    
    const cacheData = this.getCache(content.type, content.contentId || '');
    if (!cacheData) return;
    
    Object.assign(content, cacheData);
  }
}

// 创建全局缓存管理器实例
export const globalClickEventCache = new ClickEventCacheManager();

// 兼容性导出，保持向后兼容
export const ACTION_MAP = CLICK_EVENT_CONFIG;
export const EVENT_TYPE_MAP = CLICK_EVENT_MAPPINGS.clickEventTypeToActionType;
export const generateActionJson = ActionJsonGenerator.generate.bind(ActionJsonGenerator);
export const validateClickEvent = ClickEventValidator.validate.bind(ClickEventValidator);
export const validateAndCompleteClickEvent = ClickEventValidator.validateAndComplete.bind(ClickEventValidator);
export const convertClickEventToActionType = ClickEventTypeConverter.toActionType.bind(ClickEventTypeConverter); 

/**
 * 从内容对象自动提取校验所需字段
 * @param {Object} content
 * @returns {Object} 校验对象
 */
export function extractClickEventValidationFields(content) {
  if (!content) return {};

  // 根据事件类型设置正确的 actionUrl 和 actionPath
  // 优先使用 actionUrl，然后尝试从不同的字段中提取URL
  let actionUrl = content.actionUrl || content.url || content.clickEvent?.url || '';
  let actionPath = content.actionPath || content.clickEvent?.text || content.text || '';

  // 根据事件类型进行特殊处理
  if (content.type || content.actionType) {
    const eventType = content.type || content.actionType;

    switch (eventType) {
      case 'email':
      case 'OPEN_EMAIL':
        actionUrl = content.emailAddress || content.clickEvent?.emailAddress || content.email || actionUrl;
        actionPath = content.emailBody || content.clickEvent?.emailBody || actionPath;
        break;
      case 'schedule':
      case 'OPEN_SCHEDULE':
        actionUrl = content.scheduleTitle || content.clickEvent?.scheduleTitle || actionUrl;
        actionPath = content.scheduleContent || content.clickEvent?.scheduleContent || actionPath;
        break;
      case 'popup':
      case 'OPEN_POPUP':
        actionUrl = content.popupTitle || content.clickEvent?.popupTitle || actionUrl;
        actionPath = content.popupContent || content.clickEvent?.popupContent || actionPath;
        // 确保 popupButtonText 字段被正确传递
        break;
      case 'copy':
      case 'COPY_PARAMETER':
        if (content.copyType === '2' || content.copyType === 2) {
          // 固定内容
          actionUrl = content.fixedContent || content.clickEvent?.fixedContent || actionUrl;
        } else {
          // 复制参数
          actionUrl = content.selectedParamId || content.clickEvent?.selectedParamId || actionUrl;
        }
        actionPath = content.text || content.fixedContent || content.clickEvent?.text || actionPath;
        break;
      case 'sms':
      case 'OPEN_SMS':
        actionUrl = content.phone || content.clickEvent?.phone || actionUrl;
        actionPath = content.smsBody || content.clickEvent?.smsBody || actionPath;
        break;
    }
  }

  return {
    actionType: content.actionType || content.clickEvent?.type,
    actionUrl: actionUrl,
    actionPath: actionPath,
    packageName: content.packageName || content.clickEvent?.packageName,
    floorType: content.floorType || content.clickEvent?.floorType || '0',
    emailAddress: content.emailAddress || content.clickEvent?.emailAddress || content.email,
    emailSubject: content.emailSubject || content.clickEvent?.emailSubject,
    emailBody: content.emailBody || content.clickEvent?.emailBody,
    scheduleTitle: content.scheduleTitle || content.clickEvent?.scheduleTitle,
    scheduleContent: content.scheduleContent || content.clickEvent?.scheduleContent,
    scheduleStartTimeString: content.scheduleStartTimeString || content.clickEvent?.scheduleStartTimeString,
    scheduleEndTimeString: content.scheduleEndTimeString || content.clickEvent?.scheduleEndTimeString,
    popupTitle: content.popupTitle || content.clickEvent?.popupTitle,
    popupContent: content.popupContent || content.clickEvent?.popupContent,
    popupButtonText: content.popupButtonText || content.clickEvent?.popupButtonText,
    copyType: content.copyType || content.clickEvent?.copyType,
    selectedParamId: content.selectedParamId || content.clickEvent?.selectedParamId,
    fixedContent: content.fixedContent || content.clickEvent?.fixedContent
  };
}