<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击事件设置修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .test-step {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .issue {
            color: #dc3545;
            font-weight: bold;
        }
        .fix {
            color: #007bff;
            font-weight: bold;
        }
        .code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">点击事件设置修复测试方案</h1>
        
        <div class="test-case">
            <h3>问题1：复制内容中选中固定内容后切换面板被重置为复制参数</h3>
            
            <div class="test-step">
                <strong>测试步骤：</strong>
                <ol>
                    <li>打开多图文模板编辑器</li>
                    <li>选择一个图片内容项</li>
                    <li>在点击事件设置中选择"复制内容"</li>
                    <li>选择"固定内容"并输入内容（如"123"）</li>
                    <li>切换到其他内容项（如按钮）</li>
                    <li>再切换回原来的图片内容项</li>
                </ol>
            </div>
            
            <div class="test-step issue">
                <strong>原问题：</strong>
                复制类型被重置为"复制参数"，固定内容丢失
            </div>
            
            <div class="test-step expected">
                <strong>期望结果：</strong>
                复制类型保持为"固定内容"，输入的内容"123"仍然存在
            </div>
            
            <div class="test-step fix">
                <strong>修复方案：</strong>
                <ul>
                    <li>修改 <span class="code">initializeFields</span> 函数中的逻辑</li>
                    <li>始终保存 <span class="code">fixedContent</span> 和 <span class="code">selectedParamId</span></li>
                    <li>不再根据 <span class="code">copyType</span> 清空另一种模式的数据</li>
                    <li>改进缓存键生成，确保每个内容项独立缓存</li>
                </ul>
            </div>
        </div>

        <div class="test-case">
            <h3>问题2：多图文模板中不同内容的点击事件数据被共享</h3>
            
            <div class="test-step">
                <strong>测试步骤：</strong>
                <ol>
                    <li>打开多图文模板编辑器</li>
                    <li>选择第一个图片，设置点击事件为"复制内容" -> "固定内容" -> "图片内容"</li>
                    <li>选择按钮，设置点击事件为"跳转邮箱" -> 输入邮箱地址</li>
                    <li>选择第二个图片，设置点击事件为"打开弹窗" -> 输入弹窗标题和内容</li>
                    <li>在不同内容项之间切换</li>
                </ol>
            </div>
            
            <div class="test-step issue">
                <strong>原问题：</strong>
                不同内容项的点击事件设置相互影响，数据被共享
            </div>
            
            <div class="test-step expected">
                <strong>期望结果：</strong>
                每个内容项的点击事件设置独立，不相互影响
            </div>
            
            <div class="test-step fix">
                <strong>修复方案：</strong>
                <ul>
                    <li>修改缓存键生成逻辑：<span class="code">cardId_contentType_contentId</span></li>
                    <li>改进缓存更新逻辑，只更新当前内容项的数据</li>
                    <li>增强数据隔离机制</li>
                    <li>添加更详细的调试日志</li>
                </ul>
            </div>
        </div>

        <div class="test-case">
            <h3>验证方法</h3>
            
            <div class="test-step">
                <strong>日志检查：</strong>
                <ul>
                    <li>查看浏览器控制台中的 <span class="code">复制类型字段初始化值</span> 日志</li>
                    <li>查看 <span class="code">字段数据已保存到缓存</span> 日志中的缓存键</li>
                    <li>确认不同内容项使用不同的缓存键</li>
                </ul>
            </div>
            
            <div class="test-step">
                <strong>功能验证：</strong>
                <ul>
                    <li>固定内容在切换面板后不会丢失</li>
                    <li>复制类型选择在切换面板后保持正确</li>
                    <li>不同内容项的设置完全独立</li>
                    <li>邮箱、弹窗等其他事件类型的设置也独立</li>
                </ul>
            </div>
        </div>

        <div class="test-case">
            <h3>关键修改点</h3>
            
            <div class="test-step">
                <strong>1. 缓存键生成 (getCacheKey)：</strong>
                <pre>// 修改前：cardId_内容类型_配置类型_内容ID
// 修改后：cardId_contentType_contentId</pre>
            </div>
            
            <div class="test-step">
                <strong>2. 字段初始化逻辑：</strong>
                <pre>// 始终设置固定内容和参数ID，不管当前是哪种模式
fixedContent.value = initialFixedContent;
selectedParamId.value = initialParamId;</pre>
            </div>
            
            <div class="test-step">
                <strong>3. 缓存更新逻辑：</strong>
                <pre>// 保留现有缓存数据，只更新有值的字段
const existingCache = window.CLICK_EVENT_CACHE[cacheKey] || {};
window.CLICK_EVENT_CACHE[cacheKey] = {
    ...existingCache,
    // 只有当新内容中有值时才更新
}</pre>
            </div>
        </div>
    </div>

    <script>
        console.log('点击事件设置修复测试页面已加载');
        console.log('请按照测试步骤在实际应用中进行验证');
    </script>
</body>
</html>
