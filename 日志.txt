 传入的template: {templateId: 1, userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: {templateId: 2, userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', …}
 cardId匹配成功，返回类型: redpacket
 传入的template: {templateId: 3, userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', …}
 cardId匹配成功，返回类型: multitext
 传入的template: {templateId: 4, userId: null, appKey: null, cardId: 'com.hbm.carouse', templateName: '横滑', …}
 cardId匹配成功，返回类型: horizontalswipe
 传入的template: {templateId: 5, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext', templateName: '视频图文', …}
 cardId匹配成功，返回类型: videoimageandtext
 传入的template: {templateId: 6, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext2', templateName: '图文视频', …}
 cardId匹配成功，返回类型: videoimageandtext
 传入的template: {templateId: 7, userId: null, appKey: null, cardId: 'com.hbm.pureText', templateName: '长文本', …}
 cardId匹配成功，返回类型: longtext
 传入的template: {templateId: 8, userId: null, appKey: null, cardId: 'com.hbm.video', templateName: '视频', …}
 cardId匹配成功，返回类型: video
 传入的template: {templateId: 9, userId: null, appKey: null, cardId: 'com.hbm.carouselImageSixteenToNine', templateName: '图片轮播16:9', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 10, userId: null, appKey: null, cardId: 'com.hbm.carouselQuareImage', templateName: '图片轮播1:1', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 11, userId: null, appKey: null, cardId: 'com.hbm.carouselVerticalImage', templateName: '图片轮播48:65', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 12, userId: null, appKey: null, cardId: 'com.hbm.ecImageAndText', templateName: '电商', …}
 cardId匹配成功，返回类型: ecommerce
 传入的template: {templateId: 13, userId: null, appKey: null, cardId: 'com.hbm.ecommerce', templateName: '电商(多商品)', …}
 cardId匹配成功，返回类型: multiproduct
 传入的template: {templateId: 15, userId: null, appKey: null, cardId: 'com.hbm.cardVoucher', templateName: '单卡券', …}
 cardId匹配成功，返回类型: cardvoucher
 传入的template: {templateId: 16, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '一般通知类', …}
 cardId匹配成功，返回类型: notification
 传入的template: {templateId: 17, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '增强通知类', …}
 cardId匹配成功，返回类型: notification
 传入的template: {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 检测到的模板类型: multitext
 应用模板变更: {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 重置多图文设置为默认值: 1组图文对
 TemplateEditor - 重置所有模板设置状态，避免数据污染
 TemplateEditor - 重置全局校验失败标志
 是否为真正的模板切换: true
 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 模板类型切换检查: {当前模板类型: null, 新模板类型: 'multitext', 是否为模板类型切换: true}
 检测到模板切换，清空点击事件缓存
 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 检测到的模板类型2: multitext
 模板cardId: com.hbm.standardimageandtext
 模板内容设置完成，编辑模式: false
 handleTemplateClick: 准备发送 update-sub-type 事件，从基础模板获取的subType: 1
 handleSubTypeUpdate 被调用，接收到新的 subType: 1
 更新前的 subType 值: 
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplate.initializeContents - 开始初始化，模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 MultiTextTemplate.initializeContents - 原始内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}}
 MultiTextTemplate.initializeContents - 处理第1个内容: {type: 'image', positionNumber: 1, isTextTitle: 0, 原始role: undefined}
 MultiTextTemplate.initializeContents - 为第1个内容分配role，位置: 1, 类型: image
 MultiTextTemplate.initializeContents - 分配role: header-image
 MultiTextTemplate.initializeContents - 处理内容项: {原始: Proxy(Object), 处理后: {…}}
 MultiTextTemplate.initializeContents - 处理第2个内容: {type: 'text', positionNumber: 2, isTextTitle: 1, 原始role: undefined}
 MultiTextTemplate.initializeContents - 为第2个内容分配role，位置: 2, 类型: text
 MultiTextTemplate.initializeContents - 分配role: header-text
 MultiTextTemplate.initializeContents - 处理内容项: {原始: Proxy(Object), 处理后: {…}}
 MultiTextTemplate.initializeContents - 处理第3个内容: {type: 'image', positionNumber: 3, isTextTitle: 0, 原始role: undefined}
 MultiTextTemplate.initializeContents - 为第3个内容分配role，位置: 3, 类型: image
 MultiTextTemplate.initializeContents - 分配role: pair-image, pairIndex: 1
 MultiTextTemplate.initializeContents - 处理内容项: {原始: Proxy(Object), 处理后: {…}}
 MultiTextTemplate.initializeContents - 处理第4个内容: {type: 'text', positionNumber: 4, isTextTitle: 0, 原始role: undefined}
 MultiTextTemplate.initializeContents - 为第4个内容分配role，位置: 4, 类型: text
 MultiTextTemplate.initializeContents - 分配role: pair-text, pairIndex: 1
 MultiTextTemplate.initializeContents - 处理内容项: {原始: Proxy(Object), 处理后: {…}}
 MultiTextTemplate.initializeContents - 最终处理结果: (4) [{…}, {…}, {…}, {…}]
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 === rendererComponent 调试信息 ===
 props.templateType: multitext
 选择的渲染器组件: {__name: 'MultiTextTemplateRenderer', props: {…}, emits: Array(2), __hmrId: 'c6480d90', setup: ƒ, …}
 MultiTextTemplateRenderer - 图文对数量变化: 1
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.standardimageandtext', templateName: '多图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.standardimageandtext', templateName: '多图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'multitext-settings', type: 'multitext-settings', isMultiTextSettings: true}
 MultiTextSettings - 监听到设置变化: 1 当前值: 1
 TemplateEditor - 查找内容索引: {查找的contentId: 11, 找到的索引: 1, 当前所有内容: Array(4)}
 TemplateEditor - 查找内容索引: {查找的contentId: 13, 找到的索引: 3, 当前所有内容: Array(4)}
 MultiTextTemplateRenderer - 初始内容: Proxy(Array) {0: Proxy(Object), 1: {…}, 2: Proxy(Object), 3: {…}}
 MultiTextSettings - 组件挂载，多图文设置: Proxy(Object) {pairCount: 1}
 MultiTextSettings - 初始化pairCount为: 1
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: {…}, 2: Proxy(Object), 3: {…}}
 正在初始化内容参数显示...
 正在处理标题内容: 编辑文本，最多显示30个字。编辑文本，最多显示30个字。
 正在处理描述内容: 编辑文本，最多显示30个字。编辑，最多显示30个字。
 内容参数显示初始化完成
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示30个字。编辑文本，最多显示30个字。
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示30个字。编辑，最多显示30个字。
 点击了真正的左侧边栏，不处理，元素: 
 MultiTextSettings - pairCount变化: 1 -> 3
 MultiTextSettings - 同步到注入的设置: 3
 MultiTextTemplateRenderer - 图文对数量变化: 3
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 MultiTextSettings - 监听到设置变化: 3 当前值: 3
 MultiTextSettings - 图文对数量变化: 3
 多图文设置变更: {pairCount: 3}
 图文对数量变化: 3
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 3, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: {…}, 5: {…}, 6: {…}, 7: {…}}
 图文对数量变化: 3
 图文对数量变化: 3
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 3, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.standardimageandtext', templateName: '多图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.standardimageandtext', templateName: '多图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateEditor - 查找内容索引: {查找的contentId: 'text-2-1753843344005', 找到的索引: 5, 当前所有内容: Array(8)}
 TemplateEditor - 查找内容索引: {查找的contentId: 'text-3-1753843344005', 找到的索引: 7, 当前所有内容: Array(8)}
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: {…}, 6: Proxy(Object), 7: {…}}
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示30个字。编辑文本，最多显示30个字。
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示30个字。编辑文本，最多显示30个字。
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 保存内容设置，content类型: multitext-settings contentId: multitext-settings
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ImageSettings组件收到的appKey: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: null, result: false}
 packageName changed: 
 🍍 "param" store installed 🆕
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 没有用户输入，执行初始化字段
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 打开媒体选择弹框，当前使用的appKey: A173440270073611
 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
 切换到新的appName(父分类)，重置状态
 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100926233510024', appKey: 'A173440270073611', path: 'M175100926233510024.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
 最终使用的appKey: A173440270073611
 选择了媒体: {mediaId: 'M175100926233510024', appKey: 'A173440270073611', path: 'M175100926233510024.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 设置了视频路径: /aim_files/A173440270073611/M175100926233510024.jpeg
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: null, result: false}
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 设置事件类型(default_image_10): COPY_PARAMETER
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 非横滑模板设置事件类型为: COPY_PARAMETER {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 事件类型变化被跳过，原因: newType=COPY_PARAMETER, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 切换复制类型: 2
 复制类型未变化，跳过处理
 字段数据已保存到缓存 (image_10): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 使用的缓存键: default_image_10
 固定内容输入: 1
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_10 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 10, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 固定内容输入处理完成: {value: '1', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 packageName changed: undefined
 固定内容输入: 12
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_10 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 10, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 固定内容输入处理完成: {value: '12', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 固定内容输入: 123
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_10 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 10, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 10, 找到的索引: 0, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 固定内容输入处理完成: {value: '123', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 保存内容设置，content类型: image contentId: 10
 已保存内容设置到索引 0 类型: image actionType: COPY_PARAMETER
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: null, result: false}
 事件类型从 COPY_PARAMETER 切换到 OPEN_BROWSER，开始处理字段
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_12 {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_BROWSER'}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 字段处理完成，当前字段状态: {actionUrl: '', actionPath: '', packageName: '', emailAddress: '', popupTitle: ''}
 缓存更新成功: default_image_12 {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 packageName changed: 
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_BROWSER', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 打开媒体选择弹框，当前使用的appKey: A173440270073611
 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
 切换到新的appName(父分类)，重置状态
 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
 最终使用的appKey: A173440270073611
 选择了媒体: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 设置了视频路径: /aim_files/A173440270073611/M175100925014010023.jpeg
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_BROWSER', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 设置事件类型(default_image_12): OPEN_POPUP
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 非横滑模板设置事件类型为: OPEN_POPUP {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 事件类型变化被跳过，原因: newType=OPEN_POPUP, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '1', cachePopupTitle: '1', initialPopupTitle: '1', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '1', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '1', cachePopupTitle: '1', initialPopupTitle: '1', propsPopupContent: '2', cachePopupContent: '2', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '1', popupContent: '2', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了右侧设置面板，不处理，元素: 
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '1', cachePopupTitle: '1', initialPopupTitle: '1', propsPopupContent: '2', cachePopupContent: '2', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '1', popupContent: '2', popupButtonText: '3'}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 保存内容设置，content类型: image contentId: 12
 已保存内容设置到索引 2 类型: image actionType: OPEN_POPUP
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: {…}, 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_BROWSER', result: false}
 事件类型从 OPEN_POPUP 切换到 OPEN_BROWSER，开始处理字段
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753843344005', actionType: 'OPEN_BROWSER'}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 字段处理完成，当前字段状态: {actionUrl: '', actionPath: '', packageName: '', emailAddress: '', popupTitle: ''}
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 packageName changed: 
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: true, isCouponProductTemplate: false}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_BROWSER', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 设置事件类型(default_image_image-2-1753843344005): OPEN_POPUP
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 非横滑模板设置事件类型为: OPEN_POPUP {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 事件类型变化被跳过，原因: newType=OPEN_POPUP, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753843344005', actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753843344005', actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '5', cachePopupContent: '5', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: ''}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753843344005', actionType: 'OPEN_POPUP'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '5', cachePopupContent: '5', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 保存内容设置，content类型: image contentId: image-2-1753843344005
 已保存内容设置到索引 4 类型: image actionType: OPEN_POPUP
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: {…}, 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '1', cachePopupTitle: '1', initialPopupTitle: '4', propsPopupContent: '2', cachePopupContent: '2', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 保存内容设置，content类型: image contentId: 12
 已保存内容设置到索引 2 类型: image actionType: OPEN_POPUP
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: {…}, 3: Proxy(Object), 4: {…}, 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '5', cachePopupContent: '5', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 打开媒体选择弹框，当前使用的appKey: A173440270073611
 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
 切换到新的appName(父分类)，重置状态
 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100923797710022', appKey: 'A173440270073611', path: 'M175100923797710022.png', mediaType: 'image', contentType: 'image/png', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
 最终使用的appKey: A173440270073611
 选择了媒体: {mediaId: 'M175100923797710022', appKey: 'A173440270073611', path: 'M175100923797710022.png', mediaType: 'image', contentType: 'image/png', …}
 设置了视频路径: /aim_files/A173440270073611/M175100923797710022.png
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', pageId: 1, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', pageId: 1, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '5', cachePopupContent: '5', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
 设置为固定内容模式: {copyType: '2', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 点击了middle-section中的空白区域（预览容器外部）
 多图文模板：显示多图文设置面板
 === handleDialogContentClick 结束 - middle-section处理完成 ===
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'multitext-settings', type: 'multitext-settings', isMultiTextSettings: true}
 缓存更新成功: default_image_image-2-1753843344005 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753843344005', pageId: 1, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753843344005', 找到的索引: 4, 当前所有内容: Array(8)}
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 设置同步到内容完成: {contentId: 'image-2-1753843344005', contentType: 'image'}
 组件切换或同模板内操作，保留字段数据
 MultiTextSettings - 监听到设置变化: 3 当前值: 1
 MultiTextSettings - 更新本地状态为: 3
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (3) [{…}, {…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object)}
 MultiTextSettings - 组件挂载，多图文设置: Proxy(Object) {pairCount: 3}
 MultiTextSettings - 初始化pairCount为: 3
 MultiTextSettings - pairCount变化: 3 -> 2
 MultiTextSettings - 同步到注入的设置: 2
 MultiTextTemplateRenderer - 图文对数量变化: 2
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 MultiTextSettings - 监听到设置变化: 2 当前值: 2
 MultiTextSettings - 图文对数量变化: 2
 多图文设置变更: {pairCount: 2}
 图文对数量变化: 2
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 2, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 图文对数量变化: 2
 图文对数量变化: 2
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 2, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 保存内容设置，content类型: multitext-settings contentId: multitext-settings
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753843344005', pageId: 1, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
ImageSettings.vue:115 ImageSettings组件收到的appKey: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753843344005', type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2432 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1524 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
ClickEventSettings.vue:1537 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '4', propsPopupContent: '5', cachePopupContent: '5', …}
ClickEventSettings.vue:2170 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2201 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2170 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2201 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1572 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
ClickEventSettings.vue:1588 设置为固定内容模式: {copyType: '2', fixedContent: ''}
ClickEventSettings.vue:2432 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2242 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: false, isCouponProductTemplate: false}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
ClickEventSettings.vue:1893 检测到用户输入，跳过初始化字段以避免覆盖用户数据
TemplateEditor.vue:3934 保存内容设置，content类型: image contentId: image-2-1753843344005
TemplateEditor.vue:3998 已保存内容设置到索引 4 类型: image actionType: OPEN_POPUP
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: {…}, 5: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2432 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1524 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '2', currentFixedContent: '', …}
ClickEventSettings.vue:1537 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '1', cachePopupTitle: '1', initialPopupTitle: '4', propsPopupContent: '2', cachePopupContent: '2', …}
ClickEventSettings.vue:2170 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2201 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2170 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2201 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1572 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '4', popupContent: '5', popupButtonText: '6'}
ClickEventSettings.vue:1588 设置为固定内容模式: {copyType: '2', fixedContent: ''}
ClickEventSettings.vue:2432 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
