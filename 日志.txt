 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 检测到的模板类型: imageandtext
 应用模板变更: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 重置多图文设置为默认值: 1组图文对
 TemplateEditor - 重置所有模板设置状态，避免数据污染
 TemplateEditor - 重置全局校验失败标志
 是否为真正的模板切换: true
 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 模板类型切换检查: {当前模板类型: null, 新模板类型: 'imageandtext', 是否为模板类型切换: true}
 检测到模板切换，清空点击事件缓存
 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 检测到的模板类型2: imageandtext
 模板cardId: com.hbm.imageandtext
 模板内容设置完成，编辑模式: false
 handleTemplateClick: 准备发送 update-sub-type 事件，从基础模板获取的subType: 1
 handleSubTypeUpdate 被调用，接收到新的 subType: 1
 更新前的 subType 值: 1
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 === rendererComponent 调试信息 ===
 props.templateType: imageandtext
 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: '图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 TemplateEditor - 查找内容索引: {查找的contentId: 2, 找到的索引: 1, 当前所有内容: Array(4)}
 TemplateEditor - 查找内容索引: {查找的contentId: 3, 找到的索引: 2, 当前所有内容: Array(4)}
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 正在初始化内容参数显示...
 正在处理标题内容: 编辑文本，最多显示17个字
 正在处理描述内容: 编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。
 内容参数显示初始化完成
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示17个字
 TextElement: 编辑模式，进行参数格式化 编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。
 点击了真正的左侧边栏，不处理，元素: 
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: {…}, 2: {…}, 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ImageSettings组件收到的appKey: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: null, result: false}
 packageName changed: 
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 没有用户输入，执行初始化字段
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 打开媒体选择弹框，当前使用的appKey: A173440270073611
 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
 切换到新的appName(父分类)，重置状态
 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
 最终使用的appKey: A173440270073611
 选择了媒体: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 设置了视频路径: /aim_files/A173440270073611/M175100925014010023.jpeg
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: null, result: false}
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 设置事件类型(default_default_image_1): COPY_PARAMETER
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 非横滑模板设置事件类型为: COPY_PARAMETER {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 事件类型变化被跳过，原因: newType=COPY_PARAMETER, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 切换复制类型: 2
 复制类型未变化，跳过处理
 字段数据已保存到缓存 (image_1): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 固定内容输入: 1
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_image_1 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '1', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 packageName changed: undefined
 固定内容输入: 12
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_image_1 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '12', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 固定内容输入: 123
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_image_1 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '123', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 保存内容设置，content类型: image contentId: 1
 已保存内容设置到索引 0 类型: image actionType: COPY_PARAMETER
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 缓存更新成功: default_default_image_1 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 设置同步到内容完成: {contentId: 1, contentType: 'image'}
 组件切换或同模板内操作，保留字段数据
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: null, result: false}
 packageName changed: 
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 没有用户输入，执行初始化字段
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 保存内容设置，content类型: button contentId: 4
 已保存内容设置到索引 3 类型: button actionType: null
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: {…}}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ImageSettings组件收到的appKey: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
 packageName changed: undefined
 缓存更新成功: default_default_button_4 {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 设置同步到内容完成: {contentId: 4, contentType: 'button'}
 组件切换或同模板内操作，保留字段数据
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: '123'}
 packageName changed: Proxy(Array) {}
 点击了预览容器内部，检查是否为可编辑内容，容器: 
 点击了预览容器内的非可编辑区域，交由预览容器处理
 没有用户输入，执行初始化字段
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '123', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: '123'}
 切换复制类型: 2
 复制类型未变化，跳过处理
 字段数据已保存到缓存 (image_1): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 保存内容设置，content类型: image contentId: 1
 已保存内容设置到索引 0 类型: image actionType: COPY_PARAMETER
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 缓存更新成功: default_default_image_1 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 设置同步到内容完成: {contentId: 1, contentType: 'image'}
 组件切换或同模板内操作，保留字段数据
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: 'OPEN_BROWSER', result: false}
 packageName changed: undefined
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 没有用户输入，执行初始化字段
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 设置事件类型(default_default_button_4): COPY_PARAMETER
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 非横滑模板设置事件类型为: COPY_PARAMETER {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: 'COPY_PARAMETER', result: false}
 事件类型变化被跳过，原因: newType=COPY_PARAMETER, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 packageName changed: Proxy(Array) {}
 切换复制类型: 2
 复制类型未变化，跳过处理
 字段数据已保存到缓存 (button_4): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 固定内容输入: 4
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_button_4 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'button', contentId: 4, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '4', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: 'COPY_PARAMETER', result: false}
 packageName changed: undefined
 固定内容输入: 45
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_button_4 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'button', contentId: 4, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '45', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: 'COPY_PARAMETER', result: false}
 固定内容输入: 456
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_default_button_4 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'button', contentId: 4, actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 固定内容输入处理完成: {value: '456', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
 cardId匹配成功，返回类型: imageandtext
 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
ClickEventSettings.vue:712 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 4, type: 'button', actionType: 'COPY_PARAMETER', result: false}
TemplateEditor.vue:3934 保存内容设置，content类型: button contentId: 4
TemplateEditor.vue:3998 已保存内容设置到索引 3 类型: button actionType: COPY_PARAMETER
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: {…}}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ImageSettings.vue:115 ImageSettings组件收到的appKey: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
ClickEventSettings.vue:712 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'COPY_PARAMETER', result: false}
ClickEventSettings.vue:2418 packageName changed: undefined
ClickEventSettings.vue:1190 缓存更新成功: default_default_button_4 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 4, pageId: 1, templateId: 1, type: 'button', content: '编辑按钮', …}
TemplateEditor.vue:1835 TemplateEditor - 查找内容索引: {查找的contentId: 4, 找到的索引: 3, 当前所有内容: Array(4)}
TemplateEditor.vue:1953 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:2334 设置同步到内容完成: {contentId: 4, contentType: 'button'}
ClickEventSettings.vue:2020 组件切换或同模板内操作，保留字段数据
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:1503 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1519 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1530 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2156 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2187 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2156 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2187 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1565 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1586 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: '123'}
ClickEventSettings.vue:2418 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
ClickEventSettings.vue:1876 没有用户输入，执行初始化字段
ClickEventSettings.vue:1503 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1519 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: undefined, finalCopyType: '1', currentFixedContent: '123', …}
ClickEventSettings.vue:1530 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2156 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2187 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2156 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2187 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1565 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1586 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: '123'}
ClickEventSettings.vue:1237 切换复制类型: 2
ClickEventSettings.vue:1250 复制类型未变化，跳过处理
ClickEventSettings.vue:542 字段数据已保存到缓存 (image_1): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
