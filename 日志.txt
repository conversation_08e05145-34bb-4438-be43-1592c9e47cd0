ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2351 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1835 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: [{…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:2236 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2426 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1534 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '2', cachePopupContent: '2', …}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '2', popupButtonText: '3'}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2351 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: [{…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:2236 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2426 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1534 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '22', cachePopupContent: '22', …}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '3'}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2351 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1835 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(4)}
TemplateEditor.vue:1872 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 12, actionType: 'OPEN_POPUP'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: [{…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:2236 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: false, isCouponProductTemplate: false}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2426 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1534 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '22', cachePopupContent: '22', …}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '32'}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
TemplateEditor.vue:3934 保存内容设置，content类型: image contentId: 12
TemplateEditor.vue:3998 已保存内容设置到索引 2 类型: image actionType: OPEN_POPUP
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: [{…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: {…}, 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 10, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 10, type: 'image', actionType: 'OPEN_POPUP', result: false}
ClickEventSettings.vue:2426 packageName changed: undefined
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1534 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '4', cachePopupTitle: '4', initialPopupTitle: '11', propsPopupContent: '5', cachePopupContent: '5', …}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '32'}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
TemplateEditor.vue:3934 保存内容设置，content类型: image contentId: 10
TemplateEditor.vue:3998 已保存内容设置到索引 0 类型: image actionType: OPEN_POPUP
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: Proxy(Object), 2: {…}, 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '22', cachePopupContent: '22', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '32'}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
ImageSettings.vue:189 打开媒体选择弹框，当前使用的appKey: A173440270073611
MediaSelectorDialog.vue:249 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
MediaSelectorDialog.vue:401 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
MediaSelectorDialog.vue:416 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
MediaCategory.vue:372 切换到新的appName(父分类)，重置状态
MediaCategory.vue:654 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
MediaCategory.vue:441 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
MediaSelectorDialog.vue:341 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100922626810021', appKey: 'A173440270073611', path: 'M175100922626810021.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
MediaSelectorDialog.vue:454 最终使用的appKey: A173440270073611
ImageSettings.vue:214 选择了媒体: {mediaId: 'M175100922626810021', appKey: 'A173440270073611', path: 'M175100922626810021.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 设置了视频路径: /aim_files/A173440270073611/M175100922626810021.jpeg
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(4)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 packageName changed: undefined
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '22', cachePopupContent: '22', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '32'}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
 点击了middle-section中的空白区域（预览容器外部）
 多图文模板：显示多图文设置面板
 === handleDialogContentClick 结束 - middle-section处理完成 ===
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'multitext-settings', type: 'multitext-settings', isMultiTextSettings: true}
 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 12, 找到的索引: 2, 当前所有内容: Array(4)}
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
 设置同步到内容完成: {contentId: 12, contentType: 'image'}
 组件切换或同模板内操作，保留字段数据
 MultiTextSettings - 监听到设置变化: 1 当前值: 1
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
MultiTextSettings.vue:94 MultiTextSettings - 组件挂载，多图文设置: Proxy(Object) {pairCount: 1}
MultiTextSettings.vue:99 MultiTextSettings - 初始化pairCount为: 1
MultiTextSettings.vue:48 MultiTextSettings - pairCount变化: 1 -> 2
MultiTextSettings.vue:53 MultiTextSettings - 同步到注入的设置: 2
 MultiTextTemplateRenderer - 图文对数量变化: 2
 MultiTextTemplateRenderer - 生成的图文对: [{…}]
 MultiTextSettings - 监听到设置变化: 2 当前值: 2
 MultiTextSettings - 图文对数量变化: 2
 多图文设置变更: {pairCount: 2}
 图文对数量变化: 2
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 2, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: {…}, 5: {…}}
 图文对数量变化: 2
 图文对数量变化: 2
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 多图文模板图文对数量变化，调整内容: {模板: '多图文', 新数量: 2, 当前内容: Proxy(Array)}
 ensureTextImagePairs - 生成的内容: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
 多图文内容更新完成: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.standardimageandtext', templateName: '多图文', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 TemplateEditor - 查找内容索引: {查找的contentId: 'text-2-1753842892370', 找到的索引: 5, 当前所有内容: Array(6)}
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: {…}}
 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示30个字。编辑文本，最多显示30个字。
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
TemplateEditor.vue:3934 保存内容设置，content类型: multitext-settings contentId: multitext-settings
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/aim_defult/defaultImg.jpg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ImageSettings组件收到的appKey: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'OPEN_BROWSER', result: false}
 packageName changed: 
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
 没有用户输入，执行初始化字段
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ImageSettings.vue:189 打开媒体选择弹框，当前使用的appKey: A173440270073611
 重置媒体选择器状态
 重置状态时设置selectedAppKey: A173440270073611
 媒体选择器状态已重置: {selectedAppKey: 'A173440270073611', mediaType: 'image', numType: 1}
 获取分类列表，filterByAppKey: true filterAppKey: A173440270073611
 按appKey过滤后的分类数据, 原始数量: 23 过滤后数量: 5 appKey: A173440270073611
 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
 从分类组件获取到appKey和dirId: A173440270073611 
MediaSelectorDialog.vue:401 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
MediaSelectorDialog.vue:416 没有接收到dirId，不更新dirId和请求数据
 处理父级分类勾选变化，appName: xjk_test checked: true
MediaCategory.vue:372 切换到新的appName(父分类)，重置状态
MediaCategory.vue:654 发送分类变化事件，选中的分类数量: 5
 分类变化: (5) [{…}, {…}, {…}, {…}, {…}]
 开始获取媒体列表，选中的分类: Proxy(Array) {0: 934, 1: 516, 2: 515, 3: 447, 4: 1006}
 获取素材列表参数: {appKey: 'A173440270073611', dirIds: '934,516,515,447,1006', dirType: 2, numType: 1, page: 1, …}
 从分类组件获取到appKey和dirId: A173440270073611 934,516,515,447,1006
 已固定appKey: A173440270073611 忽略从分类组件传来的appKey: A173440270073611
 判断是否为基础版式: false 选中的dirIds: (5) [934, 516, 515, 447, 1006]
MediaCategory.vue:441 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
MediaSelectorDialog.vue:341 获取素材列表结果: {code: 0, msg: '成功', data: {…}}
 选择了媒体文件: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
 使用的appKey优先级: 媒体自身的appKey= A173440270073611 当前选中的appKey= A173440270073611 props传入的appKey= A173440270073611
 最终使用的appKey: A173440270073611
ImageSettings.vue:214 选择了媒体: {mediaId: 'M175100925014010023', appKey: 'A173440270073611', path: 'M175100925014010023.jpeg', mediaType: 'image', contentType: 'image/jpeg', …}
ImageSettings.vue:229 设置了视频路径: /aim_files/A173440270073611/M175100925014010023.jpeg
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: undefined, cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 设置事件类型(default_image_image-2-1753842892370): COPY_PARAMETER
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
TemplateEditor.vue:1835 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 非横滑模板设置事件类型为: COPY_PARAMETER {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'COPY_PARAMETER', result: false}
 事件类型变化被跳过，原因: newType=COPY_PARAMETER, oldType=OPEN_BROWSER, _isUpdatingContent=false, _contentChanging=false, _isInitializingFields=false, _isSettingActionType=true
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:1239 切换复制类型: 2
ClickEventSettings.vue:1252 复制类型未变化，跳过处理
ClickEventSettings.vue:543 字段数据已保存到缓存 (image_image-2-1753842892370): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
ClickEventSettings.vue:544 使用的缓存键: default_image_image-2-1753842892370
 固定内容输入: 1
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2351 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753842892370 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753842892370', actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 固定内容输入处理完成: {value: '1', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'COPY_PARAMETER', result: false}
 packageName changed: undefined
ClickEventSettings.vue:1319 固定内容输入: 12
ClickEventSettings.vue:1323 固定内容处理中，忽略重复请求
handleFixedContentInput @ ClickEventSettings.vue:1323
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleInput @ chunk-5CY6DC63.js?v=0ed09a29:3059
（匿名） @ chunk-5CY6DC63.js?v=0ed09a29:2853
Promise.then
nextTick @ chunk-CAGAHDR2.js?v=0ed09a29:2352
handleCompositionEnd @ chunk-5CY6DC63.js?v=0ed09a29:2853
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
 固定内容输入: 123
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
 开始updateContentWithAction
 缓存更新成功: default_image_image-2-1753842892370 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
TemplateEditor.vue:1835 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 内容更新完成: {contentType: 'image', contentId: 'image-2-1753842892370', actionType: 'COPY_PARAMETER'}
 TemplateEditor - 接收到内容设置更新: {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateEditor - 查找内容索引: {查找的contentId: 'image-2-1753842892370', 找到的索引: 4, 当前所有内容: Array(6)}
 TemplateEditor - 也更新了selectedContent引用
 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 固定内容输入处理完成: {value: '123', copyType: '2'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'COPY_PARAMETER', result: false}
TemplateEditor.vue:3934 保存内容设置，content类型: image contentId: image-2-1753842892370
TemplateEditor.vue:3998 已保存内容设置到索引 4 类型: image actionType: COPY_PARAMETER
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 cardId匹配成功，返回类型: multitext
 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: {…}, 5: Proxy(Object)}
 TemplateSettings - content 变化: Proxy(Object) {contentId: 12, pageId: 3, templateId: 3, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
 TemplateSettings - 轮播图模板判断结果: false
 TemplateSettings - 不是轮播图内容，返回false
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 12, type: 'image', actionType: 'OPEN_POPUP', result: false}
 事件类型从 COPY_PARAMETER 切换到 OPEN_POPUP，开始处理字段
 保留字段 copyType 的值，因为内容中有数据: 1
 字段处理完成，当前字段状态: {actionUrl: '', actionPath: '', packageName: undefined, emailAddress: '', popupTitle: ''}
 缓存更新成功: default_image_12 {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '2', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '2', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '11', cachePopupTitle: '11', initialPopupTitle: '11', propsPopupContent: '22', cachePopupContent: '22', …}
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_POPUP', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '11', popupContent: '22', popupButtonText: '32'}
ClickEventSettings.vue:1585 设置为固定内容模式: {copyType: '2', fixedContent: ''}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2236 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: false}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
TemplateEditor.vue:3934 保存内容设置，content类型: image contentId: 12
TemplateEditor.vue:3998 已保存内容设置到索引 2 类型: image actionType: OPEN_POPUP
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
MultiTextTemplateRenderer.vue:177 MultiTextTemplateRenderer - 生成的图文对: (2) [{…}, {…}]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: {…}, 3: Proxy(Object), 4: {…}, 5: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 'image-2-1753842892370', type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', src: '/aim_files/A173440270073611/M175100925014010023.jpeg', defaultSrc: '/aim_files/aim_defult/defaultImg.jpg', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', scene: '个性化类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 'image-2-1753842892370', type: 'image', actionType: 'COPY_PARAMETER', result: false}
ClickEventSettings.vue:1616 事件类型从 OPEN_POPUP 切换到 COPY_PARAMETER，开始处理字段
ClickEventSettings.vue:1713 字段处理完成，当前字段状态: {actionUrl: '', actionPath: '', packageName: undefined, emailAddress: '', popupTitle: ''}
ClickEventSettings.vue:1192 缓存更新成功: default_image_image-2-1753842892370 {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
ClickEventSettings.vue:2426 packageName changed: undefined
ClickEventSettings.vue:2253 批量更新被跳过: 内容变化中
ClickEventSettings.vue:1505 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1521 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '2', cacheCopyType: '2', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1534 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2164 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: ''}
ClickEventSettings.vue:2195 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1569 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1588 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: '123'}
ClickEventSettings.vue:2426 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5801 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5811 点击了预览容器内的非可编辑区域，交由预览容器处理
ClickEventSettings.vue:1239 切换复制类型: 2
ClickEventSettings.vue:1252 复制类型未变化，跳过处理
ClickEventSettings.vue:543 字段数据已保存到缓存 (image_image-2-1753842892370): {actionType: 'COPY_PARAMETER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
ClickEventSettings.vue:544 使用的缓存键: default_image_image-2-1753842892370
