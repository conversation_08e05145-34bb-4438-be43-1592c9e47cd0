 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:914 处理头部图片点击事件更新: {type: 'browser', url: ''}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
CouponProductSettings.vue:774 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.baidu.
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新: {type: 'browser', url: 'https://www.baidu.'}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 URL输入变化，立即发送更新: https://www.baidu.c
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
CouponProductSettings.vue:914 处理头部图片点击事件更新: {type: 'browser', url: 'https://www.baidu.c'}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
 内容更新进行中，跳过重复请求
 URL输入变化，立即发送更新: https://www.baidu.co
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
 内容更新进行中，跳过重复请求
 处理头部图片点击事件更新: {type: 'browser', url: 'https://www.baidu.co'}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:914 处理头部图片点击事件更新: {type: 'browser', url: ''}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
CouponProductSettings.vue:774 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.baidu.com
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新: {type: 'browser', url: 'https://www.baidu.com'}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:914 处理头部图片点击事件更新: {type: 'browser', url: ''}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 2}
CouponProductSettings.vue:774 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7345 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7378 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7381 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
TemplateEditor.vue:7565 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7566 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7373 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
TemplateEditor.vue:7345 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7378 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7381 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
TemplateEditor.vue:7565 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7566 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7373 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
ClickEventSettings.vue:2422 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6289 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6293 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: ''}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
TemplateHeader.vue:72 提交按钮点击，当前模板名称: 测试：多商品
TemplateEditor.vue:3934 保存内容设置，content类型: coupon-product-settings contentId: coupon-product-settings
TemplateEditor.vue:2929 TemplateEditor - 券+商品模板校验开始
TemplateEditor.vue:2941 TemplateEditor - 券+商品校验数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
TemplateEditor.vue:2970 校验头部图片点击事件数据: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
TemplateEditor.vue:5552 TemplateEditor - 计算券+商品设置内容
TemplateEditor.vue:5590 TemplateEditor - 使用现有券+商品设置状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 2, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
