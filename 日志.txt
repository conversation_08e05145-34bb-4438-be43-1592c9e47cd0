ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://ww'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:932 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
CouponProductSettings.vue:774 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
TemplateHeader.vue:72 提交按钮点击，当前模板名称: 测试电商
TemplateEditor.vue:3943 保存内容设置，content类型: coupon-product-settings contentId: coupon-product-settings
TemplateEditor.vue:2929 TemplateEditor - 券+商品模板校验开始
TemplateEditor.vue:2941 TemplateEditor - 券+商品校验数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:2970 校验头部图片点击事件数据: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
TemplateEditor.vue:2971 校验头部图片点击事件 - actionType: OPEN_BROWSER
TemplateEditor.vue:2972 校验头部图片点击事件 - actionUrl: https://ww
TemplateEditor.vue:2974 校验头部图片点击事件 - 校验结果: {valid: true, message: '验证通过'}
TemplateEditor.vue:5561 TemplateEditor - 计算券+商品设置内容
TemplateEditor.vue:5599 TemplateEditor - 使用现有券+商品设置状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://ww'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://ww'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:932 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
CouponProductSettings.vue:774 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
ClickEventSettings.vue:2422 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://ww'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:932 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://ww'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.b
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.b'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventEditor.vue:206 URL输入变化，立即发送更新: https://www.ba
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.b'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.ba'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:932 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.ba'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:932 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba', actionPath: '', packageName: undefined, floorType: '0', …}
CouponProductSettings.vue:749 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.bai
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.ba'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.bai'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
 其他更新进行中，跳过重复请求
 URL输入变化，立即发送更新: https://www.baid
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.bai'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baid'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 URL输入变化，立即发送更新: https://www.baidu
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baid'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu'}
CouponProductSettings.vue:923 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.baidu.
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
 其他更新进行中，跳过重复请求
 URL输入变化，立即发送更新: https://www.baidu.c
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.c'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1913 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:1929 TemplateEditor - 处理券+商品设置更新
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:2245 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: false, _isInitializingFields: true, _contentChanging: true, isCouponProductTemplate: true}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c'}
 开始updateContentWithAction
 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', packageName: Array(0), floorType: '0', …}
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.c'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.baidu.co
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.c'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.co'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.co'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
 URL输入变化，立即发送更新: https://www.baidu.com
 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.co'}
 其他更新进行中，跳过重复请求
 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', …}
 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.com'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7354 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
TemplateEditor.vue:7387 TemplateEditor - 开始处理券+商品模板数据
TemplateEditor.vue:7390 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:7574 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
TemplateEditor.vue:7575 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2257 电商模板或券商品模板内容变化中，但仍允许字段更新以避免数据丢失 {contentType: 'header-image', isCouponProductTemplate: true}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2360 开始updateContentWithAction
ClickEventSettings.vue:1192 缓存更新成功: default_header-image_header-image-click-event {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: Array(0), floorType: '0', …}
CouponProductSettings.vue:913 处理头部图片点击事件更新 - 原始event: {contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', …}
CouponProductSettings.vue:916 处理头部图片点击事件更新 - 转换后的standardClickEvent: {type: 'browser', url: 'https://www.baidu.com'}
 处理头部图片点击事件更新 - 提取后的validationContent: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: undefined, floorType: '0', …}
 处理头部图片点击事件更新 - 最终保存的imageClickEvent: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: undefined, floorType: '0', …}
 CouponProductSettings - 处理设置变化，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
 发送更新数据: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 接收到内容设置更新: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 处理券+商品设置更新
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 TemplateEditor - 接收到券+商品设置变化: {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: {…}}
 TemplateEditor - 开始处理券+商品模板数据
 TemplateEditor - 券+商品设置数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
 processCouponProductTemplateData - 商品数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}}
 TemplateEditor - 券+商品模板数据处理完成，内容数量: 22
TemplateEditor.vue:7382 TemplateEditor - 券+商品设置已更新，新状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
ClickEventSettings.vue:2422 内容更新完成: {contentType: 'header-image', contentId: 'header-image-click-event', actionType: 'OPEN_BROWSER'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6298 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6302 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:1506 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1527 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
ClickEventSettings.vue:1540 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2173 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com'}
ClickEventSettings.vue:2204 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1575 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:1594 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
TemplateHeader.vue:72 提交按钮点击，当前模板名称: 测试电商
TemplateEditor.vue:3943 保存内容设置，content类型: coupon-product-settings contentId: coupon-product-settings
TemplateEditor.vue:2929 TemplateEditor - 券+商品模板校验开始
TemplateEditor.vue:2941 TemplateEditor - 券+商品校验数据: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateEditor.vue:2970 校验头部图片点击事件数据: Proxy(Object) {actionType: 'OPEN_BROWSER', actionUrl: 'https://www.baidu.com', actionPath: '', packageName: undefined, floorType: '0', …}
TemplateEditor.vue:2971 校验头部图片点击事件 - actionType: OPEN_BROWSER
TemplateEditor.vue:2972 校验头部图片点击事件 - actionUrl: https://www.baidu.com
TemplateEditor.vue:2974 校验头部图片点击事件 - 校验结果: {valid: true, message: '验证通过'}
TemplateEditor.vue:5561 TemplateEditor - 计算券+商品设置内容
TemplateEditor.vue:5599 TemplateEditor - 使用现有券+商品设置状态: Proxy(Object) {headerProduct: Proxy(Object), coupon: {…}, products: Array(3), selectedProductIndex: 0, productCount: 3}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', scene: '个性化类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {type: 'coupon-product-settings', contentId: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
