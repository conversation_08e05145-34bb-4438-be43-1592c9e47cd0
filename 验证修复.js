// 验证点击事件设置修复的测试脚本
// 在浏览器控制台中运行此脚本来验证修复效果

console.log('=== 点击事件设置修复验证脚本 ===');

// 1. 检查缓存结构
function checkCacheStructure() {
  console.log('\n1. 检查缓存结构:');
  
  if (!window.CLICK_EVENT_CACHE) {
    console.log('❌ CLICK_EVENT_CACHE 不存在');
    return false;
  }
  
  console.log('✅ CLICK_EVENT_CACHE 存在');
  console.log('当前缓存键:', Object.keys(window.CLICK_EVENT_CACHE));
  
  // 检查缓存键格式
  const cacheKeys = Object.keys(window.CLICK_EVENT_CACHE);
  const validKeyPattern = /^[^_]+_[^_]+_[^_]+$/; // cardId_contentType_contentId
  
  let validKeys = 0;
  cacheKeys.forEach(key => {
    if (validKeyPattern.test(key)) {
      validKeys++;
      console.log(`✅ 有效缓存键: ${key}`);
    } else {
      console.log(`❌ 无效缓存键: ${key}`);
    }
  });
  
  return validKeys === cacheKeys.length;
}

// 2. 检查复制类型数据持久性
function checkCopyTypeData() {
  console.log('\n2. 检查复制类型数据:');
  
  const cacheKeys = Object.keys(window.CLICK_EVENT_CACHE || {});
  let copyParameterItems = 0;
  let fixedContentItems = 0;
  
  cacheKeys.forEach(key => {
    const cache = window.CLICK_EVENT_CACHE[key];
    if (cache.actionType === 'COPY_PARAMETER') {
      console.log(`缓存项 ${key}:`, {
        copyType: cache.copyType,
        selectedParamId: cache.selectedParamId,
        fixedContent: cache.fixedContent
      });
      
      if (cache.copyType === '1') {
        copyParameterItems++;
      } else if (cache.copyType === '2') {
        fixedContentItems++;
      }
    }
  });
  
  console.log(`复制参数模式项目: ${copyParameterItems}`);
  console.log(`固定内容模式项目: ${fixedContentItems}`);
  
  return true;
}

// 3. 模拟用户操作测试
function simulateUserActions() {
  console.log('\n3. 模拟用户操作测试:');
  
  // 模拟设置固定内容
  const testCacheKey = 'default_image_test123';
  
  if (!window.CLICK_EVENT_CACHE) {
    window.CLICK_EVENT_CACHE = {};
  }
  
  // 设置初始状态：固定内容模式
  window.CLICK_EVENT_CACHE[testCacheKey] = {
    actionType: 'COPY_PARAMETER',
    copyType: '2',
    fixedContent: '测试固定内容123',
    selectedParamId: ''
  };
  
  console.log('✅ 设置测试缓存:', window.CLICK_EVENT_CACHE[testCacheKey]);
  
  // 模拟切换到其他内容项再切换回来
  setTimeout(() => {
    const cachedData = window.CLICK_EVENT_CACHE[testCacheKey];
    if (cachedData && cachedData.copyType === '2' && cachedData.fixedContent === '测试固定内容123') {
      console.log('✅ 数据持久性测试通过: 固定内容和复制类型保持不变');
    } else {
      console.log('❌ 数据持久性测试失败:', cachedData);
    }
  }, 100);
  
  return true;
}

// 4. 检查数据隔离
function checkDataIsolation() {
  console.log('\n4. 检查数据隔离:');
  
  if (!window.CLICK_EVENT_CACHE) {
    window.CLICK_EVENT_CACHE = {};
  }
  
  // 创建两个不同的内容项
  const key1 = 'default_image_item1';
  const key2 = 'default_image_item2';
  
  window.CLICK_EVENT_CACHE[key1] = {
    actionType: 'COPY_PARAMETER',
    copyType: '2',
    fixedContent: '内容项1的固定内容'
  };
  
  window.CLICK_EVENT_CACHE[key2] = {
    actionType: 'OPEN_POPUP',
    popupTitle: '内容项2的弹窗标题',
    popupContent: '内容项2的弹窗内容'
  };
  
  // 检查数据是否独立
  const item1 = window.CLICK_EVENT_CACHE[key1];
  const item2 = window.CLICK_EVENT_CACHE[key2];
  
  if (item1.fixedContent === '内容项1的固定内容' && 
      item2.popupTitle === '内容项2的弹窗标题' &&
      !item1.popupTitle && !item2.fixedContent) {
    console.log('✅ 数据隔离测试通过: 不同内容项的数据完全独立');
    return true;
  } else {
    console.log('❌ 数据隔离测试失败');
    console.log('内容项1:', item1);
    console.log('内容项2:', item2);
    return false;
  }
}

// 5. 运行所有测试
function runAllTests() {
  console.log('开始运行修复验证测试...\n');
  
  const results = {
    cacheStructure: checkCacheStructure(),
    copyTypeData: checkCopyTypeData(),
    userActions: simulateUserActions(),
    dataIsolation: checkDataIsolation()
  };
  
  console.log('\n=== 测试结果汇总 ===');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
  
  return allPassed;
}

// 6. 提供手动测试指导
function showManualTestGuide() {
  console.log('\n=== 手动测试指导 ===');
  console.log('请按照以下步骤进行手动测试:');
  console.log('1. 打开多图文模板编辑器');
  console.log('2. 选择一个图片内容项');
  console.log('3. 设置点击事件为"复制内容"');
  console.log('4. 选择"固定内容"并输入"测试内容123"');
  console.log('5. 切换到其他内容项（如按钮）');
  console.log('6. 再切换回原来的图片内容项');
  console.log('7. 验证: 复制类型应该仍为"固定内容"，内容应该仍为"测试内容123"');
  console.log('\n如果以上步骤中数据保持不变，说明修复成功！');
}

// 导出测试函数
window.clickEventFixTests = {
  runAllTests,
  checkCacheStructure,
  checkCopyTypeData,
  simulateUserActions,
  checkDataIsolation,
  showManualTestGuide
};

// 自动运行测试
console.log('自动运行测试...');
runAllTests();
showManualTestGuide();

console.log('\n可以通过以下命令手动运行特定测试:');
console.log('- window.clickEventFixTests.runAllTests() // 运行所有测试');
console.log('- window.clickEventFixTests.checkCacheStructure() // 检查缓存结构');
console.log('- window.clickEventFixTests.showManualTestGuide() // 显示手动测试指导');
