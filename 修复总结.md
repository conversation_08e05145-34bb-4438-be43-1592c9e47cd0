# 点击事件设置问题修复总结

## 问题描述

根据日志分析，发现了两个主要问题：

### 问题1：复制内容中选中固定内容后切换面板被重置为复制参数
- **现象**：用户设置固定内容后，切换到其他设置面板再切换回来，选中状态变成了复制参数
- **原因**：`initializeFields` 函数中的逻辑有缺陷，会根据复制类型清空另一种模式的数据

### 问题2：多图文模板中不同内容的点击事件数据被共享
- **现象**：不同内容项（复制内容、跳转邮箱、跳转日程、打开弹窗）的输入内容在切换设置面板后被共享
- **原因**：缓存键生成不够精确，导致不同内容项使用了相同的缓存空间

## 修复方案

### 1. 修复缓存键生成逻辑

**文件**：`src/components/settings/ClickEventSettings.vue`

**修改位置**：第410-416行

```javascript
// 修改前
getCacheKey(contentType, contentId) {
  const currentTemplateCardId = window.TEMPLATE_DIALOG_DATA?.selectedTemplate?.cardId || 'default';
  const contentCategory = window.TEMPLATE_DIALOG_DATA?.content?.type || 'default';
  return `${currentTemplateCardId}_${contentCategory}_${contentType}_${contentId}`;
}

// 修改后
getCacheKey(contentType, contentId) {
  const currentTemplateCardId = window.TEMPLATE_DIALOG_DATA?.selectedTemplate?.cardId || 'default';
  // 使用更精确的缓存键，确保每个内容项都有独立的缓存空间
  return `${currentTemplateCardId}_${contentType}_${contentId}`;
}
```

**效果**：确保每个内容项都有独立的缓存空间，避免数据共享。

### 2. 修复字段初始化逻辑

**文件**：`src/components/settings/ClickEventSettings.vue`

**修改位置**：第1571-1585行

```javascript
// 修改前
if (initialCopyType === '2') {
  fixedContent.value = initialFixedContent;
  selectedParamId.value = '';
} else {
  selectedParamId.value = initialParamId;
  if (initialFixedContent) {
    fixedContent.value = initialFixedContent;
  }
}

// 修改后
// 始终设置固定内容和参数ID，不管当前是哪种模式
// 这样用户在两种模式间切换时不会丢失数据
fixedContent.value = initialFixedContent;
selectedParamId.value = initialParamId;
```

**效果**：用户在复制参数和固定内容之间切换时，数据不会丢失。

### 3. 改进缓存更新逻辑

**文件**：`src/components/settings/ClickEventSettings.vue`

**修改位置**：第1762-1790行

```javascript
// 修改前：直接覆盖整个缓存对象
window.CLICK_EVENT_CACHE[cacheKey] = {
  actionType: newContent.actionType || defaultActionType,
  // ... 其他字段
};

// 修改后：保留现有缓存数据，只更新有值的字段
const existingCache = window.CLICK_EVENT_CACHE[cacheKey] || {};
window.CLICK_EVENT_CACHE[cacheKey] = {
  ...existingCache,
  actionType: newContent.actionType || existingCache.actionType || defaultActionType,
  // 只有当新内容中有值时才更新
  copyType: newContent.copyType !== undefined ? newContent.copyType : (existingCache.copyType || '1'),
  selectedParamId: newContent.selectedParamId !== undefined ? newContent.selectedParamId : (existingCache.selectedParamId || ''),
  fixedContent: newContent.fixedContent !== undefined ? newContent.fixedContent : (existingCache.fixedContent || '')
};
```

**效果**：避免缓存数据被意外覆盖，保持数据的完整性。

### 4. 增强调试信息

**文件**：`src/components/settings/ClickEventSettings.vue`

**修改位置**：第533-545行

```javascript
// 添加更详细的调试信息
console.log(`字段数据已保存到缓存 (${contentType}_${contentId}):`, fieldData);
console.log(`使用的缓存键: ${cacheKey}`);
```

**效果**：便于调试和验证修复效果。

## 验证方法

### 1. 问题1验证步骤
1. 打开多图文模板编辑器
2. 选择一个图片内容项
3. 在点击事件设置中选择"复制内容"
4. 选择"固定内容"并输入内容（如"123"）
5. 切换到其他内容项（如按钮）
6. 再切换回原来的图片内容项
7. **验证**：复制类型应该保持为"固定内容"，输入的内容"123"仍然存在

### 2. 问题2验证步骤
1. 打开多图文模板编辑器
2. 选择第一个图片，设置点击事件为"复制内容" -> "固定内容" -> "图片内容"
3. 选择按钮，设置点击事件为"跳转邮箱" -> 输入邮箱地址
4. 选择第二个图片，设置点击事件为"打开弹窗" -> 输入弹窗标题和内容
5. 在不同内容项之间切换
6. **验证**：每个内容项的点击事件设置应该独立，不相互影响

### 3. 日志检查
- 查看浏览器控制台中的"复制类型字段初始化值"日志
- 查看"字段数据已保存到缓存"日志中的缓存键
- 确认不同内容项使用不同的缓存键

## 预期效果

修复后应该达到以下效果：

1. **数据持久性**：用户设置的固定内容在切换面板后不会丢失
2. **状态保持**：复制类型选择在切换面板后保持正确
3. **数据隔离**：不同内容项的设置完全独立，不相互影响
4. **功能完整**：邮箱、弹窗等其他事件类型的设置也独立工作

## 技术要点

1. **缓存键设计**：使用 `cardId_contentType_contentId` 格式确保唯一性
2. **数据保护**：在更新缓存时保留现有数据，避免意外覆盖
3. **状态管理**：同时保存复制参数和固定内容，支持无缝切换
4. **调试支持**：增加详细的日志输出，便于问题排查

这些修复确保了多图文模板中点击事件设置的稳定性和独立性，提升了用户体验。
